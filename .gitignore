# Grocery Tracker - .gitignore

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~

# Dependencies
node_modules/
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Python virtual environment
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
/coverage
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Frontend builds
/build
/frontend/build
/frontend/dist

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*token.json*
*credentials.json*

# Logs and debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log
dump.rdb

# System files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.pem

# Deployment
.vercel
.netlify/

# Database files
*.db
*.sqlite
*.sqlite3

# Archive files and large assets
**/*.zip
**/*.tar.gz
**/*.tar
**/*.tgz
*.pack
*.deb
*.dylib

# Build caches
.cache/
.parcel-cache/

# Temporary files
*.tmp
*.temp