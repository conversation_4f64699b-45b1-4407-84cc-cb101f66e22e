# 🛒 Grocery Tracker

A modern, full-stack web application for tracking your grocery inventory and managing pantry items. Built with FastAPI (Python) backend and React frontend.

![Grocery Tracker](https://img.shields.io/badge/Status-Ready%20for%20Production-green)
![Python](https://img.shields.io/badge/Python-3.11+-blue)
![React](https://img.shields.io/badge/React-18+-blue)
![FastAPI](https://img.shields.io/badge/FastAPI-0.110+-green)

## ✨ Features

- **📦 Inventory Management**: Add, edit, and delete grocery items
- **📅 Expiry Tracking**: Monitor expiration dates and get alerts for items expiring soon
- **🏷️ Category Organization**: Organize items by categories (Fruits, Vegetables, Dairy, etc.)
- **📊 Dashboard Overview**: Visual dashboard with inventory statistics
- **🔍 Smart Filtering**: Filter items by category, expiry status, and more
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices
- **🎨 Modern UI**: Clean, intuitive interface built with Tailwind CSS and ShadCN UI

## 🏗️ Architecture

### Backend (FastAPI + MongoDB)
- **FastAPI**: High-performance Python web framework
- **MongoDB**: NoSQL database for flexible data storage
- **Motor**: Async MongoDB driver for Python
- **Pydantic**: Data validation and serialization
- **CORS**: Configured for frontend integration

### Frontend (React + TypeScript)
- **React 18**: Modern React with hooks and functional components
- **React Router**: Client-side routing
- **Axios**: HTTP client for API communication
- **Tailwind CSS**: Utility-first CSS framework
- **ShadCN UI**: Beautiful, accessible UI components
- **Lucide React**: Modern icon library

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** installed on your system
- **Node.js 16+** and **npm** installed
- **MongoDB** running locally (or MongoDB Atlas connection string)

### Option 1: Automated Setup (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd grocery-tracker
   ```

2. **Run the application**:
   ```bash
   # On Windows
   start-app.bat
   
   # Or run individual components
   start-backend.bat    # Backend only
   start-frontend.bat   # Frontend only
   ```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Option 2: Manual Setup

#### Backend Setup

1. **Create and activate virtual environment**:
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r backend/requirements.txt
   ```

3. **Configure environment**:
   ```bash
   # Edit backend/.env file
   MONGO_URL=mongodb://localhost:27017
   DB_NAME=grocery_tracker
   CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
   DEBUG=True
   ```

4. **Start the backend server**:
   ```bash
   uvicorn backend.server:app --reload --host 0.0.0.0 --port 8000
   ```

#### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment**:
   ```bash
   # Edit frontend/.env file
   REACT_APP_BACKEND_URL=http://localhost:8000
   ```

4. **Start the development server**:
   ```bash
   npm start
   ```

## 📚 API Documentation

### Base URL
```
http://localhost:8000/api
```

### Endpoints

#### Grocery Items

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/grocery-items` | Get all grocery items |
| POST | `/grocery-items` | Create a new grocery item |
| GET | `/grocery-items/{id}` | Get a specific grocery item |
| PUT | `/grocery-items/{id}` | Update a grocery item |
| DELETE | `/grocery-items/{id}` | Delete a grocery item |
| GET | `/grocery-items/category/{category}` | Get items by category |
| GET | `/grocery-items/expired/list` | Get all expired items |

#### Example Request Body (POST/PUT)
```json
{
  "name": "Bananas",
  "category": "Fruits",
  "quantity": 6,
  "unit": "pcs",
  "expiry_date": "2024-01-15T00:00:00Z",
  "notes": "Organic bananas from local store"
}
```

### Interactive API Documentation
Visit http://localhost:8000/docs for the complete interactive API documentation powered by Swagger UI.

## 🗂️ Project Structure

```
grocery-tracker/
├── backend/                 # FastAPI backend
│   ├── server.py           # Main FastAPI application
│   ├── requirements.txt    # Python dependencies
│   └── .env               # Backend environment variables
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API service layer
│   │   └── lib/           # Utility functions
│   ├── public/            # Static assets
│   ├── package.json       # Node.js dependencies
│   └── .env              # Frontend environment variables
├── start-app.bat          # Windows startup script
├── start-backend.bat      # Backend startup script
├── start-frontend.bat     # Frontend startup script
├── .gitignore            # Git ignore rules
└── README.md             # This file
```

## 🎯 Usage Guide

### Adding Items
1. Click the "Add Item" button on the dashboard
2. Fill in the item details:
   - **Name**: Item name (e.g., "Milk", "Bananas")
   - **Category**: Select from predefined categories
   - **Quantity & Unit**: Amount and measurement unit
   - **Expiry Date**: Optional expiration date
   - **Notes**: Additional information
3. Click "Add Item" to save

### Managing Inventory
- **View All Items**: Dashboard shows all items with status indicators
- **Edit Items**: Click the edit icon on any item card
- **Delete Items**: Click the trash icon to remove items
- **Filter by Category**: Use category filters to find specific items
- **Expired Items**: Visit the "Expired" page to see items past their expiry date

### Status Indicators
- 🟢 **Fresh**: Items with no expiry date or expiry date > 3 days away
- 🟡 **Expiring Soon**: Items expiring within 3 days
- 🔴 **Expired**: Items past their expiry date

## 🛠️ Development

### Adding New Features

1. **Backend Changes**:
   - Add new endpoints in `backend/server.py`
   - Update Pydantic models for data validation
   - Test endpoints using the interactive docs at `/docs`

2. **Frontend Changes**:
   - Add new components in `frontend/src/components/`
   - Create new pages in `frontend/src/pages/`
   - Update API service in `frontend/src/services/api.js`

### Database Schema

The application uses MongoDB with the following document structure:

```javascript
{
  "_id": ObjectId,
  "id": "uuid-string",
  "name": "Item name",
  "category": "Category name",
  "quantity": 1,
  "unit": "pcs",
  "expiry_date": ISODate,
  "added_date": ISODate,
  "is_expired": false,
  "notes": "Optional notes"
}
```

## 🚀 Deployment

### Production Deployment

#### Backend Deployment
1. Set up a production MongoDB instance
2. Configure environment variables:
   ```bash
   MONGO_URL=your-production-mongodb-url
   DB_NAME=grocery_tracker_prod
   CORS_ORIGINS=your-frontend-domain.com
   DEBUG=False
   ```
3. Deploy using services like:
   - **Heroku**: `git push heroku main`
   - **Railway**: Connect GitHub repository
   - **DigitalOcean App Platform**: Deploy from GitHub
   - **AWS EC2**: Use Docker or direct deployment

#### Frontend Deployment
1. Build the production version:
   ```bash
   cd frontend
   npm run build
   ```
2. Deploy the `build` folder to:
   - **Netlify**: Drag and drop or connect GitHub
   - **Vercel**: Connect GitHub repository
   - **AWS S3 + CloudFront**: Upload build files
   - **GitHub Pages**: Use GitHub Actions

### Environment Variables

#### Backend (.env)
```bash
MONGO_URL=mongodb://localhost:27017
DB_NAME=grocery_tracker
CORS_ORIGINS=http://localhost:3000
DEBUG=True
```

#### Frontend (.env)
```bash
REACT_APP_BACKEND_URL=http://localhost:8000
```

## 🧪 Testing

### Backend Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=backend
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

**Backend won't start:**
- Ensure MongoDB is running
- Check if port 8000 is available
- Verify Python virtual environment is activated

**Frontend won't start:**
- Ensure Node.js and npm are installed
- Delete `node_modules` and run `npm install` again
- Check if port 3000 is available

**CORS Errors:**
- Verify CORS_ORIGINS in backend/.env includes your frontend URL
- Ensure backend is running before starting frontend

**Database Connection Issues:**
- Check MongoDB connection string in backend/.env
- Ensure MongoDB service is running
- For MongoDB Atlas, verify network access settings

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page for existing solutions
2. Create a new issue with detailed information
3. Include error messages, screenshots, and system information

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the amazing Python web framework
- [React](https://reactjs.org/) for the powerful frontend library
- [ShadCN UI](https://ui.shadcn.com/) for beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for utility-first styling
- [Lucide](https://lucide.dev/) for the icon library

---

**Happy Grocery Tracking! 🛒✨**
