from fastapi import FastAP<PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Dict, Any
import uuid
from datetime import datetime


ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# In-memory database for development (replace with MongoDB in production)
# This allows the app to work without requiring MongoDB installation
in_memory_db: Dict[str, List[Dict[str, Any]]] = {
    "grocery_items": [],
    "status_checks": []
}

# Create the main app without a prefix
app = FastAPI()

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")


# Define Models
class GroceryItem(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    category: str = "Other"
    quantity: int = 1
    unit: str = "pcs"
    expiry_date: datetime = None
    added_date: datetime = Field(default_factory=datetime.utcnow)
    is_expired: bool = False
    notes: str = ""

class GroceryItemCreate(BaseModel):
    name: str
    category: str = "Other"
    quantity: int = 1
    unit: str = "pcs"
    expiry_date: datetime = None
    notes: str = ""

class GroceryItemUpdate(BaseModel):
    name: str = None
    category: str = None
    quantity: int = None
    unit: str = None
    expiry_date: datetime = None
    notes: str = None

# Keep the original StatusCheck models for backward compatibility
class StatusCheck(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    client_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class StatusCheckCreate(BaseModel):
    client_name: str

# Add your routes to the router instead of directly to app
@api_router.get("/")
async def root():
    return {"message": "Grocery Tracker API - Ready to track your pantry!"}

# Grocery Item endpoints
@api_router.post("/grocery-items", response_model=GroceryItem)
async def create_grocery_item(item: GroceryItemCreate):
    item_dict = item.dict()
    grocery_item = GroceryItem(**item_dict)

    # Check if item is expired
    if grocery_item.expiry_date and grocery_item.expiry_date < datetime.utcnow():
        grocery_item.is_expired = True

    result = await db.grocery_items.insert_one(grocery_item.dict())
    return grocery_item

@api_router.get("/grocery-items", response_model=List[GroceryItem])
async def get_grocery_items():
    items = await db.grocery_items.find().to_list(1000)
    grocery_items = []

    for item in items:
        grocery_item = GroceryItem(**item)
        # Update expired status
        if grocery_item.expiry_date and grocery_item.expiry_date < datetime.utcnow():
            grocery_item.is_expired = True
            # Update in database
            await db.grocery_items.update_one(
                {"id": grocery_item.id},
                {"$set": {"is_expired": True}}
            )
        grocery_items.append(grocery_item)

    return grocery_items

@api_router.get("/grocery-items/{item_id}", response_model=GroceryItem)
async def get_grocery_item(item_id: str):
    item = await db.grocery_items.find_one({"id": item_id})
    if not item:
        raise HTTPException(status_code=404, detail="Item not found")

    grocery_item = GroceryItem(**item)
    # Update expired status
    if grocery_item.expiry_date and grocery_item.expiry_date < datetime.utcnow():
        grocery_item.is_expired = True
        await db.grocery_items.update_one(
            {"id": grocery_item.id},
            {"$set": {"is_expired": True}}
        )

    return grocery_item

@api_router.put("/grocery-items/{item_id}", response_model=GroceryItem)
async def update_grocery_item(item_id: str, item_update: GroceryItemUpdate):
    # Get existing item
    existing_item = await db.grocery_items.find_one({"id": item_id})
    if not existing_item:
        raise HTTPException(status_code=404, detail="Item not found")

    # Update only provided fields
    update_data = {k: v for k, v in item_update.dict().items() if v is not None}

    if update_data:
        # Check if expiry date is being updated and update expired status
        if "expiry_date" in update_data:
            if update_data["expiry_date"] and update_data["expiry_date"] < datetime.utcnow():
                update_data["is_expired"] = True
            else:
                update_data["is_expired"] = False

        await db.grocery_items.update_one({"id": item_id}, {"$set": update_data})

    # Return updated item
    updated_item = await db.grocery_items.find_one({"id": item_id})
    return GroceryItem(**updated_item)

@api_router.delete("/grocery-items/{item_id}")
async def delete_grocery_item(item_id: str):
    result = await db.grocery_items.delete_one({"id": item_id})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Item not found")
    return {"message": "Item deleted successfully"}

@api_router.get("/grocery-items/category/{category}", response_model=List[GroceryItem])
async def get_items_by_category(category: str):
    items = await db.grocery_items.find({"category": category}).to_list(1000)
    return [GroceryItem(**item) for item in items]

@api_router.get("/grocery-items/expired/list", response_model=List[GroceryItem])
async def get_expired_items():
    # Update all expired items first
    current_time = datetime.utcnow()
    await db.grocery_items.update_many(
        {"expiry_date": {"$lt": current_time}},
        {"$set": {"is_expired": True}}
    )

    items = await db.grocery_items.find({"is_expired": True}).to_list(1000)
    return [GroceryItem(**item) for item in items]

# Keep original status endpoints for backward compatibility
@api_router.post("/status", response_model=StatusCheck)
async def create_status_check(input: StatusCheckCreate):
    status_dict = input.dict()
    status_obj = StatusCheck(**status_dict)
    _ = await db.status_checks.insert_one(status_obj.dict())
    return status_obj

@api_router.get("/status", response_model=List[StatusCheck])
async def get_status_checks():
    status_checks = await db.status_checks.find().to_list(1000)
    return [StatusCheck(**status_check) for status_check in status_checks]

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=os.environ.get('CORS_ORIGINS', '*').split(','),
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()
