[{"D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\index.js": "1", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\App.js": "2", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\Header.jsx": "3", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\Dashboard.jsx": "4", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\AddItem.jsx": "5", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\ExpiredItems.jsx": "6", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\toaster.jsx": "7", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\button.jsx": "8", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\services\\api.js": "9", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\card.jsx": "10", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\label.jsx": "11", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\input.jsx": "12", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\select.jsx": "13", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\lib\\utils.js": "14"}, {"size": 254, "mtime": 1758106566896, "results": "15", "hashOfConfig": "16"}, {"size": 796, "mtime": 1758106573434, "results": "17", "hashOfConfig": "16"}, {"size": 1851, "mtime": 1758106656529, "results": "18", "hashOfConfig": "16"}, {"size": 7321, "mtime": 1758106686914, "results": "19", "hashOfConfig": "16"}, {"size": 6432, "mtime": 1758106707866, "results": "20", "hashOfConfig": "16"}, {"size": 6764, "mtime": 1758106733788, "results": "21", "hashOfConfig": "16"}, {"size": 154, "mtime": 1758106645243, "results": "22", "hashOfConfig": "16"}, {"size": 1611, "mtime": 1758106596952, "results": "23", "hashOfConfig": "16"}, {"size": 1534, "mtime": 1758106587797, "results": "24", "hashOfConfig": "16"}, {"size": 1495, "mtime": 1758106605125, "results": "25", "hashOfConfig": "16"}, {"size": 538, "mtime": 1758106617090, "results": "26", "hashOfConfig": "16"}, {"size": 680, "mtime": 1758106611346, "results": "27", "hashOfConfig": "16"}, {"size": 4747, "mtime": 1758106636262, "results": "28", "hashOfConfig": "16"}, {"size": 135, "mtime": 1758106578442, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c5t4iz", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\index.js", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\App.js", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\Header.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\Dashboard.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\AddItem.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\pages\\ExpiredItems.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\toaster.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\button.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\services\\api.js", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\card.jsx", ["72"], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\label.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\input.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\components\\ui\\select.jsx", [], [], "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\src\\lib\\utils.js", [], [], {"ruleId": "73", "severity": 1, "message": "74", "line": 27, "column": 3, "nodeType": "75", "endLine": 34, "endColumn": 5}, "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "JSXOpeningElement"]