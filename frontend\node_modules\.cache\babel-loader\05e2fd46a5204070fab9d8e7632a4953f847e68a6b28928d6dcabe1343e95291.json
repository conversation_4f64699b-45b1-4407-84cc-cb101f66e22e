{"ast": null, "code": "import $kqwpH$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { createContext as $kqwpH$createContext, forwardRef as $kqwpH$forwardRef, useContext as $kqwpH$useContext, useState as $kqwpH$useState, useEffect as $kqwpH$useEffect, createElement as $kqwpH$createElement, useRef as $kqwpH$useRef } from \"react\";\nimport { composeEventHandlers as $kqwpH$composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive as $kqwpH$Primitive, dispatchDiscreteCustomEvent as $kqwpH$dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs as $kqwpH$useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef as $kqwpH$useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown as $kqwpH$useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\nconst $5cb92bef7577960e$var$DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst $5cb92bef7577960e$var$CONTEXT_UPDATE = 'dismissableLayer.update';\nconst $5cb92bef7577960e$var$POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst $5cb92bef7577960e$var$FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\nlet $5cb92bef7577960e$var$originalBodyPointerEvents;\nconst $5cb92bef7577960e$var$DismissableLayerContext = /*#__PURE__*/$kqwpH$createContext({\n  layers: new Set(),\n  layersWithOutsidePointerEventsDisabled: new Set(),\n  branches: new Set()\n});\nconst $5cb92bef7577960e$export$177fb62ff3ec1f22 = /*#__PURE__*/$kqwpH$forwardRef((props, forwardedRef) => {\n  var _node$ownerDocument;\n  const {\n    disableOutsidePointerEvents = false,\n    onEscapeKeyDown: onEscapeKeyDown,\n    onPointerDownOutside: onPointerDownOutside,\n    onFocusOutside: onFocusOutside,\n    onInteractOutside: onInteractOutside,\n    onDismiss: onDismiss,\n    ...layerProps\n  } = props;\n  const context = $kqwpH$useContext($5cb92bef7577960e$var$DismissableLayerContext);\n  const [node1, setNode] = $kqwpH$useState(null);\n  const ownerDocument = (_node$ownerDocument = node1 === null || node1 === void 0 ? void 0 : node1.ownerDocument) !== null && _node$ownerDocument !== void 0 ? _node$ownerDocument : globalThis === null || globalThis === void 0 ? void 0 : globalThis.document;\n  const [, force] = $kqwpH$useState({});\n  const composedRefs = $kqwpH$useComposedRefs(forwardedRef, node => setNode(node));\n  const layers = Array.from(context.layers);\n  const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n  const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n  const index = node1 ? layers.indexOf(node1) : -1;\n  const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n  const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n  const pointerDownOutside = $5cb92bef7577960e$var$usePointerDownOutside(event => {\n    const target = event.target;\n    const isPointerDownOnBranch = [...context.branches].some(branch => branch.contains(target));\n    if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n    onPointerDownOutside === null || onPointerDownOutside === void 0 || onPointerDownOutside(event);\n    onInteractOutside === null || onInteractOutside === void 0 || onInteractOutside(event);\n    if (!event.defaultPrevented) onDismiss === null || onDismiss === void 0 || onDismiss();\n  }, ownerDocument);\n  const focusOutside = $5cb92bef7577960e$var$useFocusOutside(event => {\n    const target = event.target;\n    const isFocusInBranch = [...context.branches].some(branch => branch.contains(target));\n    if (isFocusInBranch) return;\n    onFocusOutside === null || onFocusOutside === void 0 || onFocusOutside(event);\n    onInteractOutside === null || onInteractOutside === void 0 || onInteractOutside(event);\n    if (!event.defaultPrevented) onDismiss === null || onDismiss === void 0 || onDismiss();\n  }, ownerDocument);\n  $kqwpH$useEscapeKeydown(event => {\n    const isHighestLayer = index === context.layers.size - 1;\n    if (!isHighestLayer) return;\n    onEscapeKeyDown === null || onEscapeKeyDown === void 0 || onEscapeKeyDown(event);\n    if (!event.defaultPrevented && onDismiss) {\n      event.preventDefault();\n      onDismiss();\n    }\n  }, ownerDocument);\n  $kqwpH$useEffect(() => {\n    if (!node1) return;\n    if (disableOutsidePointerEvents) {\n      if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n        $5cb92bef7577960e$var$originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n        ownerDocument.body.style.pointerEvents = 'none';\n      }\n      context.layersWithOutsidePointerEventsDisabled.add(node1);\n    }\n    context.layers.add(node1);\n    $5cb92bef7577960e$var$dispatchUpdate();\n    return () => {\n      if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) ownerDocument.body.style.pointerEvents = $5cb92bef7577960e$var$originalBodyPointerEvents;\n    };\n  }, [node1, ownerDocument, disableOutsidePointerEvents, context]);\n  /**\n  * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n  * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n  * and add it to the end again so the layering order wouldn't be _creation order_.\n  * We only want them to be removed from context stacks when unmounted.\n  */\n  $kqwpH$useEffect(() => {\n    return () => {\n      if (!node1) return;\n      context.layers.delete(node1);\n      context.layersWithOutsidePointerEventsDisabled.delete(node1);\n      $5cb92bef7577960e$var$dispatchUpdate();\n    };\n  }, [node1, context]);\n  $kqwpH$useEffect(() => {\n    const handleUpdate = () => force({});\n    document.addEventListener($5cb92bef7577960e$var$CONTEXT_UPDATE, handleUpdate);\n    return () => document.removeEventListener($5cb92bef7577960e$var$CONTEXT_UPDATE, handleUpdate);\n  }, []);\n  return /*#__PURE__*/$kqwpH$createElement($kqwpH$Primitive.div, $kqwpH$babelruntimehelpersesmextends({}, layerProps, {\n    ref: composedRefs,\n    style: {\n      pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? 'auto' : 'none' : undefined,\n      ...props.style\n    },\n    onFocusCapture: $kqwpH$composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n    onBlurCapture: $kqwpH$composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n    onPointerDownCapture: $kqwpH$composeEventHandlers(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n  }));\n});\n/*#__PURE__*/\nObject.assign($5cb92bef7577960e$export$177fb62ff3ec1f22, {\n  displayName: $5cb92bef7577960e$var$DISMISSABLE_LAYER_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\nconst $5cb92bef7577960e$var$BRANCH_NAME = 'DismissableLayerBranch';\nconst $5cb92bef7577960e$export$4d5eb2109db14228 = /*#__PURE__*/$kqwpH$forwardRef((props, forwardedRef) => {\n  const context = $kqwpH$useContext($5cb92bef7577960e$var$DismissableLayerContext);\n  const ref = $kqwpH$useRef(null);\n  const composedRefs = $kqwpH$useComposedRefs(forwardedRef, ref);\n  $kqwpH$useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /*#__PURE__*/$kqwpH$createElement($kqwpH$Primitive.div, $kqwpH$babelruntimehelpersesmextends({}, props, {\n    ref: composedRefs\n  }));\n});\n/*#__PURE__*/\nObject.assign($5cb92bef7577960e$export$4d5eb2109db14228, {\n  displayName: $5cb92bef7577960e$var$BRANCH_NAME\n});\n/* -----------------------------------------------------------------------------------------------*/ /**\n                                                                                                     * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n                                                                                                     * to mimic layer dismissing behaviour present in OS.\n                                                                                                     * Returns props to pass to the node we want to check for outside events.\n                                                                                                     */\nfunction $5cb92bef7577960e$var$usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) {\n  const handlePointerDownOutside = $kqwpH$useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = $kqwpH$useRef(false);\n  const handleClickRef = $kqwpH$useRef(() => {});\n  $kqwpH$useEffect(() => {\n    const handlePointerDown = event => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = {\n          originalEvent: event\n        };\n        function handleAndDispatchPointerDownOutsideEvent() {\n          $5cb92bef7577960e$var$handleAndDispatchCustomEvent($5cb92bef7577960e$var$POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n            discrete: true\n          });\n        }\n        /**\n        * On touch devices, we need to wait for a click event because browsers implement\n        * a ~350ms delay between the time the user stops touching the display and when the\n        * browser executres events. We need to ensure we don't reactivate pointer-events within\n        * this timeframe otherwise the browser may execute events that should have been prevented.\n        *\n        * Additionally, this also lets us deal automatically with cancellations when a click event\n        * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n        *\n        * This is why we also continuously remove the previous listener, because we cannot be\n        * certain that it was raised, and therefore cleaned-up.\n        */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, {\n            once: true\n          });\n        } else handleAndDispatchPointerDownOutsideEvent();\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n    * if this hook executes in a component that mounts via a `pointerdown` event, the event\n    * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n    * this by delaying the event listener registration on the document.\n    * This is not React specific, but rather how the DOM works, ie:\n    * ```\n    * button.addEventListener('pointerdown', () => {\n    *   console.log('I will log');\n    *   document.addEventListener('pointerdown', () => {\n    *     console.log('I will also log');\n    *   })\n    * });\n    */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction $5cb92bef7577960e$var$useFocusOutside(onFocusOutside, ownerDocument = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) {\n  const handleFocusOutside = $kqwpH$useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = $kqwpH$useRef(false);\n  $kqwpH$useEffect(() => {\n    const handleFocus = event => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = {\n          originalEvent: event\n        };\n        $5cb92bef7577960e$var$handleAndDispatchCustomEvent($5cb92bef7577960e$var$FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction $5cb92bef7577960e$var$dispatchUpdate() {\n  const event = new CustomEvent($5cb92bef7577960e$var$CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction $5cb92bef7577960e$var$handleAndDispatchCustomEvent(name, handler, detail, {\n  discrete: discrete\n}) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, {\n    bubbles: false,\n    cancelable: true,\n    detail: detail\n  });\n  if (handler) target.addEventListener(name, handler, {\n    once: true\n  });\n  if (discrete) $kqwpH$dispatchDiscreteCustomEvent(target, event);else target.dispatchEvent(event);\n}\nconst $5cb92bef7577960e$export$be92b6f5f03c0fe9 = $5cb92bef7577960e$export$177fb62ff3ec1f22;\nconst $5cb92bef7577960e$export$aecb2ddcb55c95be = $5cb92bef7577960e$export$4d5eb2109db14228;\nexport { $5cb92bef7577960e$export$177fb62ff3ec1f22 as DismissableLayer, $5cb92bef7577960e$export$4d5eb2109db14228 as DismissableLayerBranch, $5cb92bef7577960e$export$be92b6f5f03c0fe9 as Root, $5cb92bef7577960e$export$aecb2ddcb55c95be as Branch };", "map": {"version": 3, "names": ["$5cb92bef7577960e$var$DISMISSABLE_LAYER_NAME", "$5cb92bef7577960e$var$CONTEXT_UPDATE", "$5cb92bef7577960e$var$POINTER_DOWN_OUTSIDE", "$5cb92bef7577960e$var$FOCUS_OUTSIDE", "$5cb92bef7577960e$var$originalBodyPointerEvents", "$5cb92bef7577960e$var$DismissableLayerContext", "$kqwpH$createContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "$5cb92bef7577960e$export$177fb62ff3ec1f22", "$kqwpH$forwardRef", "props", "forwardedRef", "_node$ownerDocument", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "context", "$kqwpH$useContext", "node1", "setNode", "$kqwpH$useState", "ownerDocument", "globalThis", "document", "force", "composedRefs", "$kqwpH$useComposedRefs", "node", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "index", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "$5cb92bef7577960e$var$usePointerDownOutside", "event", "target", "isPointerDownOnBranch", "some", "branch", "contains", "defaultPrevented", "focusOutside", "$5cb92bef7577960e$var$useFocusOutside", "isFocusInBranch", "$kqwpH$useEscapeKeydown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "$kqwpH$useEffect", "body", "style", "pointerEvents", "add", "$5cb92bef7577960e$var$dispatchUpdate", "delete", "handleUpdate", "addEventListener", "removeEventListener", "$kqwpH$createElement", "$kqwpH$Primitive", "div", "$kqwpH$babelruntimehelpersesmextends", "ref", "undefined", "onFocusCapture", "$kqwpH$composeEventHandlers", "onBlurCapture", "onPointerDownCapture", "Object", "assign", "displayName", "$5cb92bef7577960e$var$BRANCH_NAME", "$5cb92bef7577960e$export$4d5eb2109db14228", "$kqwpH$useRef", "current", "usePointerDownOutside", "handlePointerDownOutside", "$kqwpH$useCallbackRef", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "eventDetail", "originalEvent", "handleAndDispatchPointerDownOutsideEvent", "$5cb92bef7577960e$var$handleAndDispatchCustomEvent", "discrete", "pointerType", "once", "timerId", "window", "setTimeout", "clearTimeout", "useFocusOutside", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "dispatchUpdate", "CustomEvent", "dispatchEvent", "handleAndDispatchCustomEvent", "name", "handler", "detail", "bubbles", "cancelable", "$kqwpH$dispatchDiscreteCustomEvent", "$5cb92bef7577960e$export$be92b6f5f03c0fe9", "$5cb92bef7577960e$export$aecb2ddcb55c95be"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-dismissable-layer\\dist\\packages\\react\\dismissable-layer\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-dismissable-layer\\dist\\packages\\react\\dismissable-layer\\src\\DismissableLayer.tsx"], "sourcesContent": ["export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n"], "mappings": ";;;;;;;;ACSA;;;AAIA,MAAMA,4CAAsB,GAAG,kBAA/B;AACA,MAAMC,oCAAc,GAAG,yBAAvB;AACA,MAAMC,0CAAoB,GAAG,qCAA7B;AACA,MAAMC,mCAAa,GAAG,+BAAtB;AAEA,IAAIC,+CAAJ;AAEA,MAAMC,6CAAuB,gBAAGC,oBAAA,CAAoB;EAClDC,MAAM,EAAE,IAAIC,GAAJ,EAD0C;EAElDC,sCAAsC,EAAE,IAAID,GAAJ,EAFU;EAGlDE,QAAQ,EAAE,IAAIF,GAAJ;CAHoB,CAAhC;AA0CA,MAAMG,yCAAgB,gBAAGC,iBAAA,CACvB,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAAA,IAAAC,mBAAA;EACvB,MAAM;IACJC,2BAA2B,GAAG,KAD1B;qBAEJC,eAFI;0BAGJC,oBAHI;oBAIJC,cAJI;uBAKJC,iBALI;eAMJC,SANI;IAOJ,GAAGC;EAAH,CAPI,GAQFT,KARJ;EASA,MAAMU,OAAO,GAAGC,iBAAA,CAAiBnB,6CAAjB,CAAhB;EACA,MAAM,CAACoB,KAAD,EAAOC,OAAP,IAAkBC,eAAA,CAA+C,IAA/C,CAAxB;EACA,MAAMC,aAAa,IAAAb,mBAAA,GAAGU,KAAH,aAAGA,KAAH,uBAAGA,KAAI,CAAEG,aAAT,cAAAb,mBAAA,cAAAA,mBAAA,GAA0Bc,UAA1B,aAA0BA,UAA1B,uBAA0BA,UAAU,CAAEC,QAAzD;EACA,MAAM,GAAGC,KAAH,IAAYJ,eAAA,CAAe,EAAf,CAAlB;EACA,MAAMK,YAAY,GAAGC,sBAAe,CAACnB,YAAD,EAAgBoB,IAAD,IAAUR,OAAO,CAACQ,IAAD,CAAhC,CAApC;EACA,MAAM3B,MAAM,GAAG4B,KAAK,CAACC,IAAN,CAAWb,OAAO,CAAChB,MAAnB,CAAf;EACA,MAAM,CAAC8B,4CAAD,IAAiD,C,GAAId,OAAO,CAACd,sCAAZ,EAAoD6B,KAApD,CAA0D,EAA1D,CAAvD,CAhBuB,CAgB+F;EACtH,MAAMC,iDAAiD,GAAGhC,MAAM,CAACiC,OAAP,CAAeH,4CAAf,CAA1D,CAjBuB,CAiBiG;EACxH,MAAMI,KAAK,GAAGhB,KAAI,GAAGlB,MAAM,CAACiC,OAAP,CAAef,KAAf,CAAH,GAA0B,EAA5C;EACA,MAAMiB,2BAA2B,GAAGnB,OAAO,CAACd,sCAAR,CAA+CkC,IAA/C,GAAsD,CAA1F;EACA,MAAMC,sBAAsB,GAAGH,KAAK,IAAIF,iDAAxC;EAEA,MAAMM,kBAAkB,GAAGC,2CAAqB,CAAEC,KAAD,IAAW;IAC1D,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,MAAMC,qBAAqB,GAAG,C,GAAI1B,OAAO,CAACb,QAAZ,EAAsBwC,IAAtB,CAA4BC,MAAD,IAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC,CAA9B;IACA,IAAI,CAACJ,sBAAD,IAA2BK,qBAA/B,EAAsD;IACtD/B,oBAAoB,SAApB,IAAAA,oBAAoB,WAApB,IAAAA,oBAAoB,CAAG6B,KAAH,CAApB;IACA3B,iBAAiB,SAAjB,IAAAA,iBAAiB,WAAjB,IAAAA,iBAAiB,CAAG2B,KAAH,CAAjB;IACA,IAAI,CAACA,KAAK,CAACM,gBAAX,EAA6BhC,SAAS,SAAT,IAAAA,SAAS,WAAT,IAAAA,SAAS,EAAtC;GAN8C,EAO7CO,aAP6C,CAAhD;EASA,MAAM0B,YAAY,GAAGC,qCAAe,CAAER,KAAD,IAAW;IAC9C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,MAAMQ,eAAe,GAAG,C,GAAIjC,OAAO,CAACb,QAAZ,EAAsBwC,IAAtB,CAA4BC,MAAD,IAAYA,MAAM,CAACC,QAAP,CAAgBJ,MAAhB,CAAvC,CAAxB;IACA,IAAIQ,eAAJ,EAAqB;IACrBrC,cAAc,SAAd,IAAAA,cAAc,WAAd,IAAAA,cAAc,CAAG4B,KAAH,CAAd;IACA3B,iBAAiB,SAAjB,IAAAA,iBAAiB,WAAjB,IAAAA,iBAAiB,CAAG2B,KAAH,CAAjB;IACA,IAAI,CAACA,KAAK,CAACM,gBAAX,EAA6BhC,SAAS,SAAT,IAAAA,SAAS,WAAT,IAAAA,SAAS,EAAtC;GANkC,EAOjCO,aAPiC,CAApC;EASA6B,uBAAgB,CAAEV,KAAD,IAAW;IAC1B,MAAMW,cAAc,GAAGjB,KAAK,KAAKlB,OAAO,CAAChB,MAAR,CAAeoC,IAAf,GAAsB,CAAvD;IACA,IAAI,CAACe,cAAL,EAAqB;IACrBzC,eAAe,SAAf,IAAAA,eAAe,WAAf,IAAAA,eAAe,CAAG8B,KAAH,CAAf;IACA,IAAI,CAACA,KAAK,CAACM,gBAAP,IAA2BhC,SAA/B,EAA0C;MACxC0B,KAAK,CAACY,cAAN;MACAtC,SAAS,EAAT;;GANY,EAQbO,aARa,CAAhB;EAUAgC,gBAAA,CAAgB,MAAM;IACpB,IAAI,CAACnC,KAAL,EAAW;IACX,IAAIT,2BAAJ,EAAiC;MAC/B,IAAIO,OAAO,CAACd,sCAAR,CAA+CkC,IAA/C,KAAwD,CAA5D,EAA+D;QAC7DvC,+CAAyB,GAAGwB,aAAa,CAACiC,IAAd,CAAmBC,KAAnB,CAAyBC,aAArD;QACAnC,aAAa,CAACiC,IAAd,CAAmBC,KAAnB,CAAyBC,aAAzB,GAAyC,MAAzC;;MAEFxC,OAAO,CAACd,sCAAR,CAA+CuD,GAA/C,CAAmDvC,KAAnD;;IAEFF,OAAO,CAAChB,MAAR,CAAeyD,GAAf,CAAmBvC,KAAnB;IACAwC,oCAAc,EAAd;IACA,OAAO,MAAM;MACX,IACEjD,2BAA2B,IAC3BO,OAAO,CAACd,sCAAR,CAA+CkC,IAA/C,KAAwD,CAF1D,EAIEf,aAAa,CAACiC,IAAd,CAAmBC,KAAnB,CAAyBC,aAAzB,GAAyC3D,+CAAzC;KALJ;GAXF,EAmBG,CAACqB,KAAD,EAAOG,aAAP,EAAsBZ,2BAAtB,EAAmDO,OAAnD,CAnBH,CAmBC;EAED;;;;;;EAMAqC,gBAAA,CAAgB,MAAM;IACpB,OAAO,MAAM;MACX,IAAI,CAACnC,KAAL,EAAW;MACXF,OAAO,CAAChB,MAAR,CAAe2D,MAAf,CAAsBzC,KAAtB;MACAF,OAAO,CAACd,sCAAR,CAA+CyD,MAA/C,CAAsDzC,KAAtD;MACAwC,oCAAc,EAAd;KAJF;GADF,EAOG,CAACxC,KAAD,EAAOF,OAAP,CAPH,CAOC;EAEDqC,gBAAA,CAAgB,MAAM;IACpB,MAAMO,YAAY,GAAGA,CAAA,KAAMpC,KAAK,CAAC,EAAD,CAAhC;IACAD,QAAQ,CAACsC,gBAAT,CAA0BnE,oCAA1B,EAA0CkE,YAA1C;IACA,OAAO,MAAMrC,QAAQ,CAACuC,mBAAT,CAA6BpE,oCAA7B,EAA6CkE,YAA7C,CAAb;GAHF,EAIG,EAJH,CAIC;EAED,oBACEG,oBAAA,CAACC,gBAAD,CAAWC,GAAX,EAAAC,oCAAA,KACMnD,UADN,EADF;IAGIoD,GAAG,EAAE1C,YAFP;IAGE8B,KAAK,EAAE;MACLC,aAAa,EAAErB,2BAA2B,GACtCE,sBAAsB,GACpB,MADoB,GAEpB,MAHoC,GAItC+B,SALC;MAML,GAAG9D,KAAK,CAACiD;KATb;IAWEc,cAAc,EAAEC,2BAAoB,CAAChE,KAAK,CAAC+D,cAAP,EAAuBtB,YAAY,CAACsB,cAApC,CAXtC;IAYEE,aAAa,EAAED,2BAAoB,CAAChE,KAAK,CAACiE,aAAP,EAAsBxB,YAAY,CAACwB,aAAnC,CAZrC;IAaEC,oBAAoB,EAAEF,2BAAoB,CACxChE,KAAK,CAACkE,oBADkC,EAExClC,kBAAkB,CAACkC,oBAFqB;GAb5C,EADF;CA7FqB,CAAzB;AAoHA;AAAAC,MAAA,CAAAC,MAAA,CAAAtE,yCAAA;EAAAuE,WAAA,EAAAlF;CAAA;AAEA;;;AAIA,MAAMmF,iCAAW,GAAG,wBAApB;AAKA,MAAMC,yCAAsB,gBAAGxE,iBAAA,CAG7B,CAACC,KAAD,EAAQC,YAAR,KAAyB;EACzB,MAAMS,OAAO,GAAGC,iBAAA,CAAiBnB,6CAAjB,CAAhB;EACA,MAAMqE,GAAG,GAAGW,aAAA,CAA4C,IAA5C,CAAZ;EACA,MAAMrD,YAAY,GAAGC,sBAAe,CAACnB,YAAD,EAAe4D,GAAf,CAApC;EAEAd,gBAAA,CAAgB,MAAM;IACpB,MAAM1B,IAAI,GAAGwC,GAAG,CAACY,OAAjB;IACA,IAAIpD,IAAJ,EAAU;MACRX,OAAO,CAACb,QAAR,CAAiBsD,GAAjB,CAAqB9B,IAArB;MACA,OAAO,MAAM;QACXX,OAAO,CAACb,QAAR,CAAiBwD,MAAjB,CAAwBhC,IAAxB;OADF;;GAJJ,EAQG,CAACX,OAAO,CAACb,QAAT,CARH,CAQC;EAED,oBAAO4D,oBAAA,CAACC,gBAAD,CAAWC,GAAX,EAAAC,oCAAA,KAAmB5D,KAAnB,EAAP;IAAiC6D,GAAG,EAAE1C;GAA/B,EAAP;CAlB6B,CAA/B;AAqBA;AAAAgD,MAAA,CAAAC,MAAA,CAAAG,yCAAA;EAAAF,WAAA,EAAAC;CAAA;AAEA,qGAKA;;;;;AAKA,SAASrC,2CAATyC,CACErE,oBADF,EAEEU,aAAuB,GAAGC,UAAH,aAAGA,UAAH,uBAAGA,UAAU,CAAEC,QAFxC,EAGE;EACA,MAAM0D,wBAAwB,GAAGC,qBAAc,CAACvE,oBAAD,CAA/C;EACA,MAAMwE,2BAA2B,GAAGL,aAAA,CAAa,KAAb,CAApC;EACA,MAAMM,cAAc,GAAGN,aAAA,CAAa,MAAM,EAAnB,CAAvB;EAEAzB,gBAAA,CAAgB,MAAM;IACpB,MAAMgC,iBAAiB,GAAI7C,KAAD,IAAyB;MACjD,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC0C,2BAA2B,CAACJ,OAAjD,EAA0D;QACxD,MAAMO,WAAW,GAAG;UAAEC,aAAa,EAAE/C;SAArC;QAEA,SAASgD,wCAATA,CAAA,EAAoD;UAClDC,kDAA4B,CAC1B9F,0CAD0B,EAE1BsF,wBAF0B,EAG1BK,WAH0B,EAI1B;YAAEI,QAAQ,EAAE;WAJc,CAA5B;;QAQF;;;;;;;;;;;;QAYA,IAAIlD,KAAK,CAACmD,WAAN,KAAsB,OAA1B,EAAmC;UACjCtE,aAAa,CAACyC,mBAAd,CAAkC,OAAlC,EAA2CsB,cAAc,CAACL,OAA1D;UACAK,cAAc,CAACL,OAAf,GAAyBS,wCAAzB;UACAnE,aAAa,CAACwC,gBAAd,CAA+B,OAA/B,EAAwCuB,cAAc,CAACL,OAAvD,EAAgE;YAAEa,IAAI,EAAE;WAAxE,CAAgE;SAHlE,MAKEJ,wCAAwC,EAAxC;;MAGJL,2BAA2B,CAACJ,OAA5B,GAAsC,KAAtC;KAjCF;IAmCA;;;;;;;;;;;;;IAaA,MAAMc,OAAO,GAAGC,MAAM,CAACC,UAAP,CAAkB,MAAM;MACtC1E,aAAa,CAACwC,gBAAd,CAA+B,aAA/B,EAA8CwB,iBAA9C;KADc,EAEb,CAFa,CAAhB;IAGA,OAAO,MAAM;MACXS,MAAM,CAACE,YAAP,CAAoBH,OAApB;MACAxE,aAAa,CAACyC,mBAAd,CAAkC,aAAlC,EAAiDuB,iBAAjD;MACAhE,aAAa,CAACyC,mBAAd,CAAkC,OAAlC,EAA2CsB,cAAc,CAACL,OAA1D;KAHF;GApDF,EAyDG,CAAC1D,aAAD,EAAgB4D,wBAAhB,CAzDH,CAyDC;EAED,OAAO;IACL;IACAT,oBAAoB,EAAEA,CAAA,KAAOW,2BAA2B,CAACJ,OAA5B,GAAsC;GAFrE;;AAMF;;;;AAIA,SAAS/B,qCAATiD,CACErF,cADF,EAEES,aAAuB,GAAGC,UAAH,aAAGA,UAAH,uBAAGA,UAAU,CAAEC,QAFxC,EAGE;EACA,MAAM2E,kBAAkB,GAAGhB,qBAAc,CAACtE,cAAD,CAAzC;EACA,MAAMuF,yBAAyB,GAAGrB,aAAA,CAAa,KAAb,CAAlC;EAEAzB,gBAAA,CAAgB,MAAM;IACpB,MAAM+C,WAAW,GAAI5D,KAAD,IAAuB;MACzC,IAAIA,KAAK,CAACC,MAAN,IAAgB,CAAC0D,yBAAyB,CAACpB,OAA/C,EAAwD;QACtD,MAAMO,WAAW,GAAG;UAAEC,aAAa,EAAE/C;SAArC;QACAiD,kDAA4B,CAAC7F,mCAAD,EAAgBsG,kBAAhB,EAAoCZ,WAApC,EAAiD;UAC3EI,QAAQ,EAAE;SADgB,CAA5B;;KAHJ;IAQArE,aAAa,CAACwC,gBAAd,CAA+B,SAA/B,EAA0CuC,WAA1C;IACA,OAAO,MAAM/E,aAAa,CAACyC,mBAAd,CAAkC,SAAlC,EAA6CsC,WAA7C,CAAb;GAVF,EAWG,CAAC/E,aAAD,EAAgB6E,kBAAhB,CAXH,CAWC;EAED,OAAO;IACL7B,cAAc,EAAEA,CAAA,KAAO8B,yBAAyB,CAACpB,OAA1B,GAAoC,IADtD;IAELR,aAAa,EAAEA,CAAA,KAAO4B,yBAAyB,CAACpB,OAA1B,GAAoC;GAF5D;;AAMF,SAASrB,oCAAT2C,CAAA,EAA0B;EACxB,MAAM7D,KAAK,GAAG,IAAI8D,WAAJ,CAAgB5G,oCAAhB,CAAd;EACA6B,QAAQ,CAACgF,aAAT,CAAuB/D,KAAvB;;AAGF,SAASiD,kDAATe,CACEC,IADF,EAEEC,OAFF,EAGEC,MAHF,EAIE;YAAEjB;AAAA,CAJJ,EAKE;EACA,MAAMjD,MAAM,GAAGkE,MAAM,CAACpB,aAAP,CAAqB9C,MAApC;EACA,MAAMD,KAAK,GAAG,IAAI8D,WAAJ,CAAgBG,IAAhB,EAAsB;IAAEG,OAAO,EAAE,KAAX;IAAkBC,UAAU,EAAE,IAA9B;YAAoCF;GAA1D,CAAd;EACA,IAAID,OAAJ,EAAajE,MAAM,CAACoB,gBAAP,CAAwB4C,IAAxB,EAA8BC,OAA9B,EAAwD;IAAEd,IAAI,EAAE;GAAhE,CAAwD;EAErE,IAAIF,QAAJ,EACEoB,kCAA2B,CAACrE,MAAD,EAASD,KAAT,CAA3B,MAEAC,MAAM,CAAC8D,aAAP,CAAqB/D,KAArB;;AAIJ,MAAMuE,yCAAI,GAAG3G,yCAAb;AACA,MAAM4G,yCAAM,GAAGnC,yCAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}