{"ast": null, "code": "/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nfunction mergeConfigs(baseConfig, configExtension) {\n  for (var key in configExtension) {\n    mergePropertyRecursively(baseConfig, key, configExtension[key]);\n  }\n  return baseConfig;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar overrideTypes = /*#__PURE__*/new Set(['string', 'number', 'boolean']);\nfunction mergePropertyRecursively(baseObject, mergeKey, mergeValue) {\n  if (!hasOwnProperty.call(baseObject, mergeKey) || overrideTypes.has(typeof mergeValue) || mergeValue === null) {\n    baseObject[mergeKey] = mergeValue;\n    return;\n  }\n  if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n    baseObject[mergeKey] = baseObject[mergeKey].concat(mergeValue);\n    return;\n  }\n  if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n    if (baseObject[mergeKey] === null) {\n      baseObject[mergeKey] = mergeValue;\n      return;\n    }\n    for (var nextKey in mergeValue) {\n      mergePropertyRecursively(baseObject[mergeKey], nextKey, mergeValue[nextKey]);\n    }\n  }\n}\nexport { mergeConfigs };", "map": {"version": 3, "names": ["mergeConfigs", "baseConfig", "configExtension", "key", "mergePropertyRecursively", "hasOwnProperty", "Object", "prototype", "overrideTypes", "Set", "baseObject", "mergeKey", "mergeValue", "call", "has", "Array", "isArray", "concat", "<PERSON><PERSON><PERSON>"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\merge-configs.ts"], "sourcesContent": ["import { Config } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport function mergeConfigs(baseConfig: Config, configExtension: Partial<Config>) {\n    for (const key in configExtension) {\n        mergePropertyRecursively(baseConfig as any, key, configExtension[key as keyof Config])\n    }\n\n    return baseConfig\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst overrideTypes = new Set(['string', 'number', 'boolean'])\n\nfunction mergePropertyRecursively(\n    baseObject: Record<string, unknown>,\n    mergeKey: string,\n    mergeValue: unknown,\n) {\n    if (\n        !hasOwnProperty.call(baseObject, mergeKey) ||\n        overrideTypes.has(typeof mergeValue) ||\n        mergeValue === null\n    ) {\n        baseObject[mergeKey] = mergeValue\n        return\n    }\n\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = (baseObject[mergeKey] as unknown[]).concat(mergeValue)\n        return\n    }\n\n    if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue\n            return\n        }\n\n        for (const nextKey in mergeValue) {\n            mergePropertyRecursively(\n                baseObject[mergeKey] as Record<string, unknown>,\n                nextKey,\n                mergeValue[nextKey as keyof object],\n            )\n        }\n    }\n}\n"], "mappings": "AAEA;;;AAGG;AACa,SAAAA,YAAYA,CAACC,UAAkB,EAAEC,eAAgC;EAC7E,KAAK,IAAMC,GAAG,IAAID,eAAe,EAAE;IAC/BE,wBAAwB,CAACH,UAAiB,EAAEE,GAAG,EAAED,eAAe,CAACC,GAAmB,CAAC,CAAC;EACzF;EAED,OAAOF,UAAU;AACrB;AAEA,IAAMI,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACtD,IAAMG,aAAa,gBAAG,IAAIC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAE9D,SAASL,wBAAwBA,CAC7BM,UAAmC,EACnCC,QAAgB,EAChBC,UAAmB;EAEnB,IACI,CAACP,cAAc,CAACQ,IAAI,CAACH,UAAU,EAAEC,QAAQ,CAAC,IAC1CH,aAAa,CAACM,GAAG,CAAC,OAAOF,UAAU,CAAC,IACpCA,UAAU,KAAK,IAAI,EACrB;IACEF,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU;IACjC;EACH;EAED,IAAIG,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,IAAIG,KAAK,CAACC,OAAO,CAACN,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE;IAClED,UAAU,CAACC,QAAQ,CAAC,GAAID,UAAU,CAACC,QAAQ,CAAe,CAACM,MAAM,CAACL,UAAU,CAAC;IAC7E;EACH;EAED,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAI,OAAOF,UAAU,CAACC,QAAQ,CAAC,KAAK,QAAQ,EAAE;IAC5E,IAAID,UAAU,CAACC,QAAQ,CAAC,KAAK,IAAI,EAAE;MAC/BD,UAAU,CAACC,QAAQ,CAAC,GAAGC,UAAU;MACjC;IACH;IAED,KAAK,IAAMM,OAAO,IAAIN,UAAU,EAAE;MAC9BR,wBAAwB,CACpBM,UAAU,CAACC,QAAQ,CAA4B,EAC/CO,OAAO,EACPN,UAAU,CAACM,OAAuB,CAAC,CACtC;IACJ;EACJ;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}