{"ast": null, "code": "import { assignRef } from './assignRef';\nimport { createCallbackRef } from './createRef';\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link useMergeRefs} to be used in ReactComponents\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = mergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function mergeRefs(refs) {\n  return createCallbackRef(function (newValue) {\n    return refs.forEach(function (ref) {\n      return assignRef(ref, newValue);\n    });\n  });\n}", "map": {"version": 3, "names": ["assignRef", "createCallbackRef", "mergeRefs", "refs", "newValue", "for<PERSON>ach", "ref"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-callback-ref/dist/es2015/mergeRef.js"], "sourcesContent": ["import { assignRef } from './assignRef';\nimport { createCallbackRef } from './createRef';\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link useMergeRefs} to be used in ReactComponents\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = mergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function mergeRefs(refs) {\n    return createCallbackRef(function (newValue) { return refs.forEach(function (ref) { return assignRef(ref, newValue); }); });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,iBAAiB,QAAQ,aAAa;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC5B,OAAOF,iBAAiB,CAAC,UAAUG,QAAQ,EAAE;IAAE,OAAOD,IAAI,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAON,SAAS,CAACM,GAAG,EAAEF,QAAQ,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,CAAC;AAC/H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}