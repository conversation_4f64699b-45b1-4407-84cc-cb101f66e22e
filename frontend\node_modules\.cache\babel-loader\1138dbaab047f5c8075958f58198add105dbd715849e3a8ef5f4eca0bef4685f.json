{"ast": null, "code": "import { createTailwindMerge } from './create-tailwind-merge.mjs';\nimport { getDefaultConfig } from './default-config.mjs';\nvar twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { twMerge };", "map": {"version": 3, "names": ["twMerge", "createTailwindMerge", "getDefaultConfig"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\tw-merge.ts"], "sourcesContent": ["import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "mappings": ";;IAGaA,OAAO,gBAAGC,mBAAmB,CAACC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}