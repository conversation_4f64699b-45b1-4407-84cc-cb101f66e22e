{"ast": null, "code": "import $4q5Fq$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $4q5Fq$forwardRef, useEffect as $4q5Fq$useEffect, createElement as $4q5Fq$createElement } from \"react\";\nimport { flushSync as $4q5Fq$flushSync } from \"react-dom\";\nimport { Slot as $4q5Fq$Slot } from \"@radix-ui/react-slot\";\nconst $8927f6f2acc4f386$var$NODES = ['a', 'button', 'div', 'form', 'h2', 'h3', 'img', 'input', 'label', 'li', 'nav', 'ol', 'p', 'span', 'svg', 'ul']; // Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\nconst $8927f6f2acc4f386$export$250ffa63cdc0d034 = $8927f6f2acc4f386$var$NODES.reduce((primitive, node) => {\n  const Node = /*#__PURE__*/$4q5Fq$forwardRef((props, forwardedRef) => {\n    const {\n      asChild: asChild,\n      ...primitiveProps\n    } = props;\n    const Comp = asChild ? $4q5Fq$Slot : node;\n    $4q5Fq$useEffect(() => {\n      window[Symbol.for('radix-ui')] = true;\n    }, []);\n    return /*#__PURE__*/$4q5Fq$createElement(Comp, $4q5Fq$babelruntimehelpersesmextends({}, primitiveProps, {\n      ref: forwardedRef\n    }));\n  });\n  Node.displayName = `Primitive.${node}`;\n  return {\n    ...primitive,\n    [node]: Node\n  };\n}, {});\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/ /**\n                                                                                                     * Flush custom event dispatch\n                                                                                                     * https://github.com/radix-ui/primitives/pull/1378\n                                                                                                     *\n                                                                                                     * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n                                                                                                     *\n                                                                                                     * Internally, React prioritises events in the following order:\n                                                                                                     *  - discrete\n                                                                                                     *  - continuous\n                                                                                                     *  - default\n                                                                                                     *\n                                                                                                     * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n                                                                                                     *\n                                                                                                     * `discrete` is an  important distinction as updates within these events are applied immediately.\n                                                                                                     * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n                                                                                                     * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n                                                                                                     * dispatched by another `discrete` event.\n                                                                                                     *\n                                                                                                     * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n                                                                                                     * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n                                                                                                     * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n                                                                                                     * For example:\n                                                                                                     *\n                                                                                                     * dispatching a known click 👎\n                                                                                                     * target.dispatchEvent(new Event(‘click’))\n                                                                                                     *\n                                                                                                     * dispatching a custom type within a non-discrete event 👎\n                                                                                                     * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n                                                                                                     *\n                                                                                                     * dispatching a custom type within a `discrete` event 👍\n                                                                                                     * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n                                                                                                     *\n                                                                                                     * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n                                                                                                     * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n                                                                                                     * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n                                                                                                     */\nfunction $8927f6f2acc4f386$export$6d1a0317bde7de7f(target, event) {\n  if (target) $4q5Fq$flushSync(() => target.dispatchEvent(event));\n}\n/* -----------------------------------------------------------------------------------------------*/\nconst $8927f6f2acc4f386$export$be92b6f5f03c0fe9 = $8927f6f2acc4f386$export$250ffa63cdc0d034;\nexport { $8927f6f2acc4f386$export$250ffa63cdc0d034 as Primitive, $8927f6f2acc4f386$export$be92b6f5f03c0fe9 as Root, $8927f6f2acc4f386$export$6d1a0317bde7de7f as dispatchDiscreteCustomEvent };", "map": {"version": 3, "names": ["$8927f6f2acc4f386$var$NODES", "$8927f6f2acc4f386$export$250ffa63cdc0d034", "reduce", "primitive", "node", "Node", "$4q5Fq$forwardRef", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "$4q5Fq$Slot", "$4q5Fq$useEffect", "window", "Symbol", "for", "$4q5Fq$createElement", "$4q5Fq$babelruntimehelpersesmextends", "ref", "displayName", "$8927f6f2acc4f386$export$6d1a0317bde7de7f", "dispatchDiscreteCustomEvent", "target", "event", "$4q5Fq$flushSync", "dispatchEvent", "$8927f6f2acc4f386$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-visually-hidden\\node_modules\\@radix-ui\\react-primitive\\dist\\packages\\react\\primitive\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-visually-hidden\\node_modules\\@radix-ui\\react-primitive\\dist\\packages\\react\\primitive\\src\\Primitive.tsx"], "sourcesContent": ["export {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n} from './Primitive';\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef } from './Primitive';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n"], "mappings": ";;;;ACIA,MAAMA,2BAAK,GAAG,CACZ,GADY,EAEZ,QAFY,EAGZ,KAHY,EAIZ,MAJY,EAKZ,IALY,EAMZ,IANY,EAOZ,KAPY,EAQZ,OARY,EASZ,OATY,EAUZ,IAVY,EAWZ,KAXY,EAYZ,IAZY,EAaZ,GAbY,EAcZ,MAdY,EAeZ,KAfY,EAgBZ,IAhBY,CAAd,C,CAmBA;AACA;AACA;AAcA;;;AAIA,MAAMC,yCAAS,GAAGD,2BAAK,CAACE,MAAN,CAAa,CAACC,SAAD,EAAYC,IAAZ,KAAqB;EAClD,MAAMC,IAAI,gBAAGC,iBAAA,CAAiB,CAACC,KAAD,EAA4CC,YAA5C,KAAkE;IAC9F,MAAM;eAAEC,OAAF;MAAW,GAAGC;IAAH,CAAX,GAAiCH,KAAvC;IACA,MAAMI,IAAS,GAAGF,OAAO,GAAGG,WAAH,GAAUR,IAAnC;IAEAS,gBAAA,CAAgB,MAAM;MACnBC,MAAD,CAAgBC,MAAM,CAACC,GAAP,CAAW,UAAX,CAAhB,IAA0C,IAA1C;KADF,EAEG,EAFH,CAEC;IAED,oBAAOC,oBAAA,CAACN,IAAD,EAAAO,oCAAA,KAAUR,cAAV,EAAP;MAAiCS,GAAG,EAAEX;KAA/B,EAAP;GARW,CAAb;EAWAH,IAAI,CAACe,WAAL,GAAoB,aAAYhB,IAAK,EAArC;EAEA,OAAO;IAAE,GAAGD,SAAL;IAAgB,CAACC,IAAD,GAAQC;GAA/B;CAdgB,EAef,EAfe,CAAlB;AAiBA;;oGAAA,CAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,SAASgB,yCAATC,CAA4DC,MAA5D,EAAiFC,KAAjF,EAA2F;EACzF,IAAID,MAAJ,EAAYE,gBAAA,CAAmB,MAAMF,MAAM,CAACG,aAAP,CAAqBF,KAArB,CAAzB,CAAZ;;AAGF;AAEA,MAAMG,yCAAI,GAAG1B,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}