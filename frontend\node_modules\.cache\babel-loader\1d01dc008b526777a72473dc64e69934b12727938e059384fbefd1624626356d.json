{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Merge = createLucideIcon(\"Merge\", [[\"path\", {\n  d: \"m8 6 4-4 4 4\",\n  key: \"ybng9g\"\n}], [\"path\", {\n  d: \"M12 2v10.3a4 4 0 0 1-1.172 2.872L4 22\",\n  key: \"1hyw0i\"\n}], [\"path\", {\n  d: \"m20 22-5-5\",\n  key: \"1m27yz\"\n}]]);\nexport { Merge as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\merge.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Merge\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOCA2IDQtNCA0IDQiIC8+CiAgPHBhdGggZD0iTTEyIDJ2MTAuM2E0IDQgMCAwIDEtMS4xNzIgMi44NzJMNCAyMiIgLz4KICA8cGF0aCBkPSJtMjAgMjItNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/merge\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Merge = createLucideIcon('Merge', [\n  ['path', { d: 'm8 6 4-4 4 4', key: 'ybng9g' }],\n  ['path', { d: 'M12 2v10.3a4 4 0 0 1-1.172 2.872L4 22', key: '1hyw0i' }],\n  ['path', { d: 'm20 22-5-5', key: '1m27yz' }],\n]);\n\nexport default Merge;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}