{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Linkedin = createLucideIcon(\"Linkedin\", [[\"path\", {\n  d: \"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\n  key: \"c2jq9f\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"12\",\n  x: \"2\",\n  y: \"9\",\n  key: \"mk3on5\"\n}], [\"circle\", {\n  cx: \"4\",\n  cy: \"4\",\n  r: \"2\",\n  key: \"bt5ra8\"\n}]]);\nexport { Linkedin as default };", "map": {"version": 3, "names": ["Linkedin", "createLucideIcon", "d", "key", "width", "height", "x", "y", "cx", "cy", "r"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\linkedin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Linkedin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgOGE2IDYgMCAwIDEgNiA2djdoLTR2LTdhMiAyIDAgMCAwLTItMiAyIDIgMCAwIDAtMiAydjdoLTR2LTdhNiA2IDAgMCAxIDYtNnoiIC8+CiAgPHJlY3Qgd2lkdGg9IjQiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjkiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjQiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/linkedin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Linkedin = createLucideIcon('Linkedin', [\n  [\n    'path',\n    {\n      d: 'M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z',\n      key: 'c2jq9f',\n    },\n  ],\n  ['rect', { width: '4', height: '12', x: '2', y: '9', key: 'mk3on5' }],\n  ['circle', { cx: '4', cy: '4', r: '2', key: 'bt5ra8' }],\n]);\n\nexport default Linkedin;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAU;EAAEK,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKP,GAAK;AAAA,CAAU,EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}