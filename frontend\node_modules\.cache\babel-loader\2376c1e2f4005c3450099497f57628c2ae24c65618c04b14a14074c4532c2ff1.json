{"ast": null, "code": "import $kVwnw$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $kVwnw$forwardRef, createElement as $kVwnw$createElement } from \"react\";\nimport { Primitive as $kVwnw$Primitive } from \"@radix-ui/react-primitive\";\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\nconst $ea1ef594cf570d83$var$NAME = 'VisuallyHidden';\nconst $ea1ef594cf570d83$export$439d29a4e110a164 = /*#__PURE__*/$kVwnw$forwardRef((props, forwardedRef) => {\n  return /*#__PURE__*/$kVwnw$createElement($kVwnw$Primitive.span, $kVwnw$babelruntimehelpersesmextends({}, props, {\n    ref: forwardedRef,\n    style: {\n      // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n      position: 'absolute',\n      border: 0,\n      width: 1,\n      height: 1,\n      padding: 0,\n      margin: -1,\n      overflow: 'hidden',\n      clip: 'rect(0, 0, 0, 0)',\n      whiteSpace: 'nowrap',\n      wordWrap: 'normal',\n      ...props.style\n    }\n  }));\n});\n/*#__PURE__*/\nObject.assign($ea1ef594cf570d83$export$439d29a4e110a164, {\n  displayName: $ea1ef594cf570d83$var$NAME\n});\n/* -----------------------------------------------------------------------------------------------*/\nconst $ea1ef594cf570d83$export$be92b6f5f03c0fe9 = $ea1ef594cf570d83$export$439d29a4e110a164;\nexport { $ea1ef594cf570d83$export$439d29a4e110a164 as VisuallyHidden, $ea1ef594cf570d83$export$be92b6f5f03c0fe9 as Root };", "map": {"version": 3, "names": ["$ea1ef594cf570d83$var$NAME", "$ea1ef594cf570d83$export$439d29a4e110a164", "$kVwnw$forwardRef", "props", "forwardedRef", "$kVwnw$createElement", "$kVwnw$Primitive", "span", "$kVwnw$babelruntimehelpersesmextends", "ref", "style", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "Object", "assign", "displayName", "$ea1ef594cf570d83$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-visually-hidden\\dist\\packages\\react\\visually-hidden\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-visually-hidden\\dist\\packages\\react\\visually-hidden\\src\\VisuallyHidden.tsx"], "sourcesContent": ["export {\n  VisuallyHidden,\n  //\n  Root,\n} from './VisuallyHidden';\nexport type { VisuallyHiddenProps } from './VisuallyHidden';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";;;;ACKA;;;AAIA,MAAMA,0BAAI,GAAG,gBAAb;AAMA,MAAMC,yCAAc,gBAAGC,iBAAA,CACrB,CAACC,KAAD,EAAQC,YAAR,KAAyB;EACvB,oBACEC,oBAAA,CAACC,gBAAD,CAAWC,IAAX,EAAAC,oCAAA,KACML,KADN,EADF;IAGIM,GAAG,EAAEL,YAFP;IAGEM,KAAK,EAAE;MACL;MACAC,QAAQ,EAAE,UAFL;MAGLC,MAAM,EAAE,CAHH;MAILC,KAAK,EAAE,CAJF;MAKLC,MAAM,EAAE,CALH;MAMLC,OAAO,EAAE,CANJ;MAOLC,MAAM,EAAE,EAPH;MAQLC,QAAQ,EAAE,QARL;MASLC,IAAI,EAAE,kBATD;MAULC,UAAU,EAAE,QAVP;MAWLC,QAAQ,EAAE,QAXL;MAYL,GAAGjB,KAAK,CAACO;;GAfb,EADF;CAFmB,CAAvB;AAyBA;AAAAW,MAAA,CAAAC,MAAA,CAAArB,yCAAA;EAAAsB,WAAA,EAAAvB;CAAA;AAEA;AAEA,MAAMwB,yCAAI,GAAGvB,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}