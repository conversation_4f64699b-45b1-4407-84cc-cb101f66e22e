{"ast": null, "code": "import { useRef as $8LvvK$useRef, useMemo as $8LvvK$useMemo } from \"react\";\nfunction $010c2913dbd2fe3d$export$5cae361ad82dce8b(value) {\n  const ref = $8LvvK$useRef({\n    value: value,\n    previous: value\n  }); // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return $8LvvK$useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport { $010c2913dbd2fe3d$export$5cae361ad82dce8b as usePrevious };", "map": {"version": 3, "names": ["$010c2913dbd2fe3d$export$5cae361ad82dce8b", "usePrevious", "value", "ref", "$8LvvK$useRef", "previous", "$8LvvK$useMemo", "current"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-use-previous\\dist\\packages\\react\\use-previous\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-use-previous\\dist\\packages\\react\\use-previous\\src\\usePrevious.tsx"], "sourcesContent": ["export { usePrevious } from './usePrevious';\n", "import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "mappings": ";ACEA,SAASA,yCAATC,CAAwBC,KAAxB,EAAkC;EAChC,MAAMC,GAAG,GAAGC,aAAA,CAAa;IAH3BF,KAAA,EAG6BA,KAAF;IAASG,QAAQ,EAAEH;GAAhC,CAAZ,CADgC,CAGhC;EACA;EACA;EACA,OAAOI,cAAA,CAAc,MAAM;IACzB,IAAIH,GAAG,CAACI,OAAJ,CAAYL,KAAZ,KAAsBA,KAA1B,EAAiC;MAC/BC,GAAG,CAACI,OAAJ,CAAYF,QAAZ,GAAuBF,GAAG,CAACI,OAAJ,CAAYL,KAAnC;MACAC,GAAG,CAACI,OAAJ,CAAYL,KAAZ,GAAoBA,KAApB;;IAEF,OAAOC,GAAG,CAACI,OAAJ,CAAYF,QAAnB;GALK,EAMJ,CAACH,KAAD,CANI,CAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}