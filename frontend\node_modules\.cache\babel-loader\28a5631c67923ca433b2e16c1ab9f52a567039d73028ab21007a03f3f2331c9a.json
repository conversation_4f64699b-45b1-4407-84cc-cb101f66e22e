{"ast": null, "code": "/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  var index = 0;\n  var argument;\n  var resolvedValue;\n  var string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nfunction toValue(mix) {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  var resolvedValue;\n  var string = '';\n  for (var k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nexport { twJoin };", "map": {"version": 3, "names": ["twJoin", "index", "argument", "resolvedValue", "string", "arguments", "length", "toValue", "mix", "k"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\tw-join.ts"], "sourcesContent": ["/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nfunction toValue(mix: ClassNameArray | string) {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n"], "mappings": "AAAA;;;;;;;;AAQG;SAMaA,MAAMA,CAAA;EAClB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,QAAwB;EAC5B,IAAIC,aAAqB;EACzB,IAAIC,MAAM,GAAG,EAAE;EAEf,OAAOH,KAAK,GAAGI,SAAS,CAACC,MAAM,EAAE;IAC7B,IAAKJ,QAAQ,GAAGG,SAAS,CAACJ,KAAK,EAAE,CAAC,EAAG;MACjC,IAAKE,aAAa,GAAGI,OAAO,CAACL,QAAQ,CAAC,EAAG;QACrCE,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC;QACzBA,MAAM,IAAID,aAAa;MAC1B;IACJ;EACJ;EACD,OAAOC,MAAM;AACjB;AAEA,SAASG,OAAOA,CAACC,GAA4B;EACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzB,OAAOA,GAAG;EACb;EAED,IAAIL,aAAqB;EACzB,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACF,MAAM,EAAEG,CAAC,EAAE,EAAE;IACjC,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;MACR,IAAKN,aAAa,GAAGI,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;QAC9DL,MAAM,KAAKA,MAAM,IAAI,GAAG,CAAC;QACzBA,MAAM,IAAID,aAAa;MAC1B;IACJ;EACJ;EAED,OAAOC,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}