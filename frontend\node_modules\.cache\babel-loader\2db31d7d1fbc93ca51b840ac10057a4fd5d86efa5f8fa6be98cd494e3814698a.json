{"ast": null, "code": "import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n  var options = effect && effect.options || NO_OPTIONS;\n  if (env.isNode && !options.ssr) {\n    return [null, null];\n  }\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n  var options = effect && effect.options || NO_OPTIONS;\n  var couldUseCache = env.forceCache || env.isNode && !!options.ssr || !options.async;\n  var _a = useState(couldUseCache ? function () {\n      return cache.get(importer);\n    } : undefined),\n    Car = _a[0],\n    setCar = _a[1];\n  var _b = useState(null),\n    error = _b[0],\n    setError = _b[1];\n  useEffect(function () {\n    if (!Car) {\n      importer().then(function (car) {\n        var resolved = effect ? effect.read() : car.default || car;\n        if (!resolved) {\n          console.error('Sidecar error: with importer', importer);\n          var error_1;\n          if (effect) {\n            console.error('Sidecar error: with medium', effect);\n            error_1 = new Error('Sidecar medium was not found');\n          } else {\n            error_1 = new Error('Sidecar was not found in exports');\n          }\n          setError(function () {\n            return error_1;\n          });\n          throw error_1;\n        }\n        cache.set(importer, resolved);\n        setCar(function () {\n          return resolved;\n        });\n      }, function (e) {\n        return setError(function () {\n          return e;\n        });\n      });\n    }\n  }, []);\n  return [Car, error];\n}", "map": {"version": 3, "names": ["useState", "useEffect", "env", "cache", "WeakMap", "NO_OPTIONS", "useSidecar", "importer", "effect", "options", "isNode", "ssr", "useRealSidecar", "couldUse<PERSON>ache", "forceCache", "async", "_a", "get", "undefined", "Car", "setCar", "_b", "error", "setError", "then", "car", "resolved", "read", "default", "console", "error_1", "Error", "set", "e"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-sidecar/dist/es2015/hook.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { env } from './env';\nvar cache = new WeakMap();\nvar NO_OPTIONS = {};\nexport function useSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    if (env.isNode && !options.ssr) {\n        return [null, null];\n    }\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return useRealSidecar(importer, effect);\n}\nfunction useRealSidecar(importer, effect) {\n    var options = (effect && effect.options) || NO_OPTIONS;\n    var couldUseCache = env.forceCache || (env.isNode && !!options.ssr) || !options.async;\n    var _a = useState(couldUseCache ? function () { return cache.get(importer); } : undefined), Car = _a[0], setCar = _a[1];\n    var _b = useState(null), error = _b[0], setError = _b[1];\n    useEffect(function () {\n        if (!Car) {\n            importer().then(function (car) {\n                var resolved = effect ? effect.read() : car.default || car;\n                if (!resolved) {\n                    console.error('Sidecar error: with importer', importer);\n                    var error_1;\n                    if (effect) {\n                        console.error('Sidecar error: with medium', effect);\n                        error_1 = new Error('Sidecar medium was not found');\n                    }\n                    else {\n                        error_1 = new Error('Sidecar was not found in exports');\n                    }\n                    setError(function () { return error_1; });\n                    throw error_1;\n                }\n                cache.set(importer, resolved);\n                setCar(function () { return resolved; });\n            }, function (e) { return setError(function () { return e; }); });\n        }\n    }, []);\n    return [Car, error];\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,GAAG,QAAQ,OAAO;AAC3B,IAAIC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzB,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACzC,IAAIC,OAAO,GAAID,MAAM,IAAIA,MAAM,CAACC,OAAO,IAAKJ,UAAU;EACtD,IAAIH,GAAG,CAACQ,MAAM,IAAI,CAACD,OAAO,CAACE,GAAG,EAAE;IAC5B,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;EACvB;EACA;EACA,OAAOC,cAAc,CAACL,QAAQ,EAAEC,MAAM,CAAC;AAC3C;AACA,SAASI,cAAcA,CAACL,QAAQ,EAAEC,MAAM,EAAE;EACtC,IAAIC,OAAO,GAAID,MAAM,IAAIA,MAAM,CAACC,OAAO,IAAKJ,UAAU;EACtD,IAAIQ,aAAa,GAAGX,GAAG,CAACY,UAAU,IAAKZ,GAAG,CAACQ,MAAM,IAAI,CAAC,CAACD,OAAO,CAACE,GAAI,IAAI,CAACF,OAAO,CAACM,KAAK;EACrF,IAAIC,EAAE,GAAGhB,QAAQ,CAACa,aAAa,GAAG,YAAY;MAAE,OAAOV,KAAK,CAACc,GAAG,CAACV,QAAQ,CAAC;IAAE,CAAC,GAAGW,SAAS,CAAC;IAAEC,GAAG,GAAGH,EAAE,CAAC,CAAC,CAAC;IAAEI,MAAM,GAAGJ,EAAE,CAAC,CAAC,CAAC;EACvH,IAAIK,EAAE,GAAGrB,QAAQ,CAAC,IAAI,CAAC;IAAEsB,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EACxDpB,SAAS,CAAC,YAAY;IAClB,IAAI,CAACkB,GAAG,EAAE;MACNZ,QAAQ,CAAC,CAAC,CAACiB,IAAI,CAAC,UAAUC,GAAG,EAAE;QAC3B,IAAIC,QAAQ,GAAGlB,MAAM,GAAGA,MAAM,CAACmB,IAAI,CAAC,CAAC,GAAGF,GAAG,CAACG,OAAO,IAAIH,GAAG;QAC1D,IAAI,CAACC,QAAQ,EAAE;UACXG,OAAO,CAACP,KAAK,CAAC,8BAA8B,EAAEf,QAAQ,CAAC;UACvD,IAAIuB,OAAO;UACX,IAAItB,MAAM,EAAE;YACRqB,OAAO,CAACP,KAAK,CAAC,4BAA4B,EAAEd,MAAM,CAAC;YACnDsB,OAAO,GAAG,IAAIC,KAAK,CAAC,8BAA8B,CAAC;UACvD,CAAC,MACI;YACDD,OAAO,GAAG,IAAIC,KAAK,CAAC,kCAAkC,CAAC;UAC3D;UACAR,QAAQ,CAAC,YAAY;YAAE,OAAOO,OAAO;UAAE,CAAC,CAAC;UACzC,MAAMA,OAAO;QACjB;QACA3B,KAAK,CAAC6B,GAAG,CAACzB,QAAQ,EAAEmB,QAAQ,CAAC;QAC7BN,MAAM,CAAC,YAAY;UAAE,OAAOM,QAAQ;QAAE,CAAC,CAAC;MAC5C,CAAC,EAAE,UAAUO,CAAC,EAAE;QAAE,OAAOV,QAAQ,CAAC,YAAY;UAAE,OAAOU,CAAC;QAAE,CAAC,CAAC;MAAE,CAAC,CAAC;IACpE;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACd,GAAG,EAAEG,KAAK,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}