{"ast": null, "code": "import $kY93V$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { useState as $kY93V$useState, createElement as $kY93V$createElement, forwardRef as $kY93V$forwardRef, useRef as $kY93V$useRef, useEffect as $kY93V$useEffect } from \"react\";\nimport { useFloating as $kY93V$useFloating, autoUpdate as $kY93V$autoUpdate, offset as $kY93V$offset, shift as $kY93V$shift, limitShift as $kY93V$limitShift, flip as $kY93V$flip, size as $kY93V$size, arrow as $kY93V$arrow, hide as $kY93V$hide } from \"@floating-ui/react-dom\";\nimport { Root as $kY93V$Root } from \"@radix-ui/react-arrow\";\nimport { useComposedRefs as $kY93V$useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope as $kY93V$createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive as $kY93V$Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef as $kY93V$useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect as $kY93V$useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize as $kY93V$useSize } from \"@radix-ui/react-use-size\";\nconst $cf1ac5d9fe0e8206$export$36f0086da09c4b9f = ['top', 'right', 'bottom', 'left'];\nconst $cf1ac5d9fe0e8206$export$3671ffab7b302fc9 = ['start', 'center', 'end'];\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\nconst $cf1ac5d9fe0e8206$var$POPPER_NAME = 'Popper';\nconst [$cf1ac5d9fe0e8206$var$createPopperContext, $cf1ac5d9fe0e8206$export$722aac194ae923] = $kY93V$createContextScope($cf1ac5d9fe0e8206$var$POPPER_NAME);\nconst [$cf1ac5d9fe0e8206$var$PopperProvider, $cf1ac5d9fe0e8206$var$usePopperContext] = $cf1ac5d9fe0e8206$var$createPopperContext($cf1ac5d9fe0e8206$var$POPPER_NAME);\nconst $cf1ac5d9fe0e8206$export$badac9ada3a0bdf9 = props => {\n  const {\n    __scopePopper: __scopePopper,\n    children: children\n  } = props;\n  const [anchor, setAnchor] = $kY93V$useState(null);\n  return /*#__PURE__*/$kY93V$createElement($cf1ac5d9fe0e8206$var$PopperProvider, {\n    scope: __scopePopper,\n    anchor: anchor,\n    onAnchorChange: setAnchor\n  }, children);\n};\n/*#__PURE__*/\nObject.assign($cf1ac5d9fe0e8206$export$badac9ada3a0bdf9, {\n  displayName: $cf1ac5d9fe0e8206$var$POPPER_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\nconst $cf1ac5d9fe0e8206$var$ANCHOR_NAME = 'PopperAnchor';\nconst $cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d = /*#__PURE__*/$kY93V$forwardRef((props, forwardedRef) => {\n  const {\n    __scopePopper: __scopePopper,\n    virtualRef: virtualRef,\n    ...anchorProps\n  } = props;\n  const context = $cf1ac5d9fe0e8206$var$usePopperContext($cf1ac5d9fe0e8206$var$ANCHOR_NAME, __scopePopper);\n  const ref = $kY93V$useRef(null);\n  const composedRefs = $kY93V$useComposedRefs(forwardedRef, ref);\n  $kY93V$useEffect(() => {\n    // Consumer can anchor the popper to something that isn't\n    // a DOM node e.g. pointer position, so we override the\n    // `anchorRef` with their virtual ref in this case.\n    context.onAnchorChange((virtualRef === null || virtualRef === void 0 ? void 0 : virtualRef.current) || ref.current);\n  });\n  return virtualRef ? null : /*#__PURE__*/$kY93V$createElement($kY93V$Primitive.div, $kY93V$babelruntimehelpersesmextends({}, anchorProps, {\n    ref: composedRefs\n  }));\n});\n/*#__PURE__*/\nObject.assign($cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d, {\n  displayName: $cf1ac5d9fe0e8206$var$ANCHOR_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\nconst $cf1ac5d9fe0e8206$var$CONTENT_NAME = 'PopperContent';\nconst [$cf1ac5d9fe0e8206$var$PopperContentProvider, $cf1ac5d9fe0e8206$var$useContentContext] = $cf1ac5d9fe0e8206$var$createPopperContext($cf1ac5d9fe0e8206$var$CONTENT_NAME);\nconst $cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc = /*#__PURE__*/$kY93V$forwardRef((props, forwardedRef) => {\n  var _arrowSize$width, _arrowSize$height, _middlewareData$arrow, _middlewareData$arrow2, _middlewareData$arrow3, _middlewareData$trans, _middlewareData$trans2, _middlewareData$hide;\n  const {\n    __scopePopper: __scopePopper,\n    side = 'bottom',\n    sideOffset = 0,\n    align = 'center',\n    alignOffset = 0,\n    arrowPadding = 0,\n    collisionBoundary = [],\n    collisionPadding: collisionPaddingProp = 0,\n    sticky = 'partial',\n    hideWhenDetached = false,\n    avoidCollisions = true,\n    onPlaced: onPlaced,\n    ...contentProps\n  } = props;\n  const context = $cf1ac5d9fe0e8206$var$usePopperContext($cf1ac5d9fe0e8206$var$CONTENT_NAME, __scopePopper);\n  const [content, setContent] = $kY93V$useState(null);\n  const composedRefs = $kY93V$useComposedRefs(forwardedRef, node => setContent(node));\n  const [arrow, setArrow] = $kY93V$useState(null);\n  const arrowSize = $kY93V$useSize(arrow);\n  const arrowWidth = (_arrowSize$width = arrowSize === null || arrowSize === void 0 ? void 0 : arrowSize.width) !== null && _arrowSize$width !== void 0 ? _arrowSize$width : 0;\n  const arrowHeight = (_arrowSize$height = arrowSize === null || arrowSize === void 0 ? void 0 : arrowSize.height) !== null && _arrowSize$height !== void 0 ? _arrowSize$height : 0;\n  const desiredPlacement = side + (align !== 'center' ? '-' + align : '');\n  const collisionPadding = typeof collisionPaddingProp === 'number' ? collisionPaddingProp : {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...collisionPaddingProp\n  };\n  const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n  const hasExplicitBoundaries = boundary.length > 0;\n  const detectOverflowOptions = {\n    padding: collisionPadding,\n    boundary: boundary.filter($cf1ac5d9fe0e8206$var$isNotNull),\n    // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n    altBoundary: hasExplicitBoundaries\n  };\n  const {\n    refs: refs,\n    floatingStyles: floatingStyles,\n    placement: placement,\n    isPositioned: isPositioned,\n    middlewareData: middlewareData\n  } = $kY93V$useFloating({\n    // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n    strategy: 'fixed',\n    placement: desiredPlacement,\n    whileElementsMounted: $kY93V$autoUpdate,\n    elements: {\n      reference: context.anchor\n    },\n    middleware: [$kY93V$offset({\n      mainAxis: sideOffset + arrowHeight,\n      alignmentAxis: alignOffset\n    }), avoidCollisions && $kY93V$shift({\n      mainAxis: true,\n      crossAxis: false,\n      limiter: sticky === 'partial' ? $kY93V$limitShift() : undefined,\n      ...detectOverflowOptions\n    }), avoidCollisions && $kY93V$flip({\n      ...detectOverflowOptions\n    }), $kY93V$size({\n      ...detectOverflowOptions,\n      apply: ({\n        elements: elements,\n        rects: rects,\n        availableWidth: availableWidth,\n        availableHeight: availableHeight\n      }) => {\n        const {\n          width: anchorWidth,\n          height: anchorHeight\n        } = rects.reference;\n        const contentStyle = elements.floating.style;\n        contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n        contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n        contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n        contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n      }\n    }), arrow && $kY93V$arrow({\n      element: arrow,\n      padding: arrowPadding\n    }), $cf1ac5d9fe0e8206$var$transformOrigin({\n      arrowWidth: arrowWidth,\n      arrowHeight: arrowHeight\n    }), hideWhenDetached && $kY93V$hide({\n      strategy: 'referenceHidden'\n    })]\n  });\n  const [placedSide, placedAlign] = $cf1ac5d9fe0e8206$var$getSideAndAlignFromPlacement(placement);\n  const handlePlaced = $kY93V$useCallbackRef(onPlaced);\n  $kY93V$useLayoutEffect(() => {\n    if (isPositioned) handlePlaced === null || handlePlaced === void 0 || handlePlaced();\n  }, [isPositioned, handlePlaced]);\n  const arrowX = (_middlewareData$arrow = middlewareData.arrow) === null || _middlewareData$arrow === void 0 ? void 0 : _middlewareData$arrow.x;\n  const arrowY = (_middlewareData$arrow2 = middlewareData.arrow) === null || _middlewareData$arrow2 === void 0 ? void 0 : _middlewareData$arrow2.y;\n  const cannotCenterArrow = ((_middlewareData$arrow3 = middlewareData.arrow) === null || _middlewareData$arrow3 === void 0 ? void 0 : _middlewareData$arrow3.centerOffset) !== 0;\n  const [contentZIndex, setContentZIndex] = $kY93V$useState();\n  $kY93V$useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  return /*#__PURE__*/$kY93V$createElement(\"div\", {\n    ref: refs.setFloating,\n    \"data-radix-popper-content-wrapper\": \"\",\n    style: {\n      ...floatingStyles,\n      transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)',\n      // keep off the page when measuring\n      minWidth: 'max-content',\n      zIndex: contentZIndex,\n      ['--radix-popper-transform-origin']: [(_middlewareData$trans = middlewareData.transformOrigin) === null || _middlewareData$trans === void 0 ? void 0 : _middlewareData$trans.x, (_middlewareData$trans2 = middlewareData.transformOrigin) === null || _middlewareData$trans2 === void 0 ? void 0 : _middlewareData$trans2.y].join(' ')\n    } // Floating UI interally calculates logical alignment based the `dir` attribute on\n    ,\n\n    dir: props.dir\n  }, /*#__PURE__*/$kY93V$createElement($cf1ac5d9fe0e8206$var$PopperContentProvider, {\n    scope: __scopePopper,\n    placedSide: placedSide,\n    onArrowChange: setArrow,\n    arrowX: arrowX,\n    arrowY: arrowY,\n    shouldHideArrow: cannotCenterArrow\n  }, /*#__PURE__*/$kY93V$createElement($kY93V$Primitive.div, $kY93V$babelruntimehelpersesmextends({\n    \"data-side\": placedSide,\n    \"data-align\": placedAlign\n  }, contentProps, {\n    ref: composedRefs,\n    style: {\n      ...contentProps.style,\n      // if the PopperContent hasn't been placed yet (not all measurements done)\n      // we prevent animations so that users's animation don't kick in too early referring wrong sides\n      animation: !isPositioned ? 'none' : undefined,\n      // hide the content if using the hide middleware and should be hidden\n      opacity: (_middlewareData$hide = middlewareData.hide) !== null && _middlewareData$hide !== void 0 && _middlewareData$hide.referenceHidden ? 0 : undefined\n    }\n  }))));\n});\n/*#__PURE__*/\nObject.assign($cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc, {\n  displayName: $cf1ac5d9fe0e8206$var$CONTENT_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\nconst $cf1ac5d9fe0e8206$var$ARROW_NAME = 'PopperArrow';\nconst $cf1ac5d9fe0e8206$var$OPPOSITE_SIDE = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right'\n};\nconst $cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0 = /*#__PURE__*/$kY93V$forwardRef(function $cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0(props, forwardedRef) {\n  const {\n    __scopePopper: __scopePopper,\n    ...arrowProps\n  } = props;\n  const contentContext = $cf1ac5d9fe0e8206$var$useContentContext($cf1ac5d9fe0e8206$var$ARROW_NAME, __scopePopper);\n  const baseSide = $cf1ac5d9fe0e8206$var$OPPOSITE_SIDE[contentContext.placedSide];\n  return (/*#__PURE__*/ // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    $kY93V$createElement(\"span\", {\n      ref: contentContext.onArrowChange,\n      style: {\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0'\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)'\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined\n      }\n    }, /*#__PURE__*/$kY93V$createElement($kY93V$Root, $kY93V$babelruntimehelpersesmextends({}, arrowProps, {\n      ref: forwardedRef,\n      style: {\n        ...arrowProps.style,\n        // ensures the element can be measured correctly (mostly for if SVG)\n        display: 'block'\n      }\n    })))\n  );\n});\n/*#__PURE__*/\nObject.assign($cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0, {\n  displayName: $cf1ac5d9fe0e8206$var$ARROW_NAME\n});\n/* -----------------------------------------------------------------------------------------------*/\nfunction $cf1ac5d9fe0e8206$var$isNotNull(value) {\n  return value !== null;\n}\nconst $cf1ac5d9fe0e8206$var$transformOrigin = options => ({\n  name: 'transformOrigin',\n  options: options,\n  fn(data) {\n    var _middlewareData$arrow4, _middlewareData$arrow5, _middlewareData$arrow6, _middlewareData$arrow7, _middlewareData$arrow8;\n    const {\n      placement: placement,\n      rects: rects,\n      middlewareData: middlewareData\n    } = data;\n    const cannotCenterArrow = ((_middlewareData$arrow4 = middlewareData.arrow) === null || _middlewareData$arrow4 === void 0 ? void 0 : _middlewareData$arrow4.centerOffset) !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = $cf1ac5d9fe0e8206$var$getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = {\n      start: '0%',\n      center: '50%',\n      end: '100%'\n    }[placedAlign];\n    const arrowXCenter = ((_middlewareData$arrow5 = (_middlewareData$arrow6 = middlewareData.arrow) === null || _middlewareData$arrow6 === void 0 ? void 0 : _middlewareData$arrow6.x) !== null && _middlewareData$arrow5 !== void 0 ? _middlewareData$arrow5 : 0) + arrowWidth / 2;\n    const arrowYCenter = ((_middlewareData$arrow7 = (_middlewareData$arrow8 = middlewareData.arrow) === null || _middlewareData$arrow8 === void 0 ? void 0 : _middlewareData$arrow8.y) !== null && _middlewareData$arrow7 !== void 0 ? _middlewareData$arrow7 : 0) + arrowHeight / 2;\n    let x = '';\n    let y = '';\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return {\n      data: {\n        x: x,\n        y: y\n      }\n    };\n  }\n});\nfunction $cf1ac5d9fe0e8206$var$getSideAndAlignFromPlacement(placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side, align];\n}\nconst $cf1ac5d9fe0e8206$export$be92b6f5f03c0fe9 = $cf1ac5d9fe0e8206$export$badac9ada3a0bdf9;\nconst $cf1ac5d9fe0e8206$export$b688253958b8dfe7 = $cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d;\nconst $cf1ac5d9fe0e8206$export$7c6e2c02157bb7d2 = $cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc;\nconst $cf1ac5d9fe0e8206$export$21b07c8f274aebd5 = $cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0;\nexport { $cf1ac5d9fe0e8206$export$722aac194ae923 as createPopperScope, $cf1ac5d9fe0e8206$export$badac9ada3a0bdf9 as Popper, $cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d as PopperAnchor, $cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc as PopperContent, $cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0 as PopperArrow, $cf1ac5d9fe0e8206$export$be92b6f5f03c0fe9 as Root, $cf1ac5d9fe0e8206$export$b688253958b8dfe7 as Anchor, $cf1ac5d9fe0e8206$export$7c6e2c02157bb7d2 as Content, $cf1ac5d9fe0e8206$export$21b07c8f274aebd5 as Arrow, $cf1ac5d9fe0e8206$export$36f0086da09c4b9f as SIDE_OPTIONS, $cf1ac5d9fe0e8206$export$3671ffab7b302fc9 as ALIGN_OPTIONS };", "map": {"version": 3, "names": ["$cf1ac5d9fe0e8206$export$36f0086da09c4b9f", "$cf1ac5d9fe0e8206$export$3671ffab7b302fc9", "$cf1ac5d9fe0e8206$var$POPPER_NAME", "$cf1ac5d9fe0e8206$var$createPopperContext", "$cf1ac5d9fe0e8206$export$722aac194ae923", "$kY93V$createContextScope", "$cf1ac5d9fe0e8206$var$PopperProvider", "$cf1ac5d9fe0e8206$var$usePopperContext", "$cf1ac5d9fe0e8206$export$badac9ada3a0bdf9", "props", "__scope<PERSON>opper", "children", "anchor", "setAnchor", "$kY93V$useState", "$kY93V$createElement", "scope", "onAnchorChange", "Object", "assign", "displayName", "$cf1ac5d9fe0e8206$var$ANCHOR_NAME", "$cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d", "$kY93V$forwardRef", "forwardedRef", "virtualRef", "anchorProps", "context", "ref", "$kY93V$useRef", "composedRefs", "$kY93V$useComposedRefs", "$kY93V$useEffect", "current", "$kY93V$Primitive", "div", "$kY93V$babelruntimehelpersesmextends", "$cf1ac5d9fe0e8206$var$CONTENT_NAME", "$cf1ac5d9fe0e8206$var$PopperContentProvider", "$cf1ac5d9fe0e8206$var$useContentContext", "$cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc", "_arrowSize$width", "_arrowSize$height", "_middlewareData$arrow", "_middlewareData$arrow2", "_middlewareData$arrow3", "_middlewareData$trans", "_middlewareData$trans2", "_middlewareData$hide", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "avoidCollisions", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "arrow", "setArrow", "arrowSize", "$kY93V$useSize", "arrow<PERSON>idth", "width", "arrowHeight", "height", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "Array", "isArray", "hasExplicitBoundaries", "length", "detectOverflowOptions", "padding", "filter", "$cf1ac5d9fe0e8206$var$isNotNull", "altBoundary", "refs", "floatingStyles", "placement", "isPositioned", "middlewareData", "$kY93V$useFloating", "strategy", "whileElementsMounted", "$kY93V$autoUpdate", "elements", "reference", "middleware", "$kY93V$offset", "mainAxis", "alignmentAxis", "$kY93V$shift", "crossAxis", "limiter", "$kY93V$limitShift", "undefined", "$kY93V$flip", "$kY93V$size", "apply", "rects", "availableWidth", "availableHeight", "anchorWidth", "anchorHeight", "contentStyle", "floating", "style", "setProperty", "$kY93V$arrow", "element", "$cf1ac5d9fe0e8206$var$transformOrigin", "$kY93V$hide", "placedSide", "placedAlign", "$cf1ac5d9fe0e8206$var$getSideAndAlignFromPlacement", "handlePlaced", "$kY93V$useCallbackRef", "$kY93V$useLayoutEffect", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "setFloating", "transform", "min<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "join", "dir", "onArrowChange", "shouldHideArrow", "animation", "opacity", "hide", "referenceHidden", "$cf1ac5d9fe0e8206$var$ARROW_NAME", "$cf1ac5d9fe0e8206$var$OPPOSITE_SIDE", "$cf1ac5d9fe0e8206$export$79d62cd4e10a3fd0", "PopperArrow", "arrowProps", "contentContext", "baseSide", "position", "visibility", "$kY93V$Root", "display", "isNotNull", "value", "options", "name", "fn", "data", "_middlewareData$arrow4", "_middlewareData$arrow5", "_middlewareData$arrow6", "_middlewareData$arrow7", "_middlewareData$arrow8", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "getSideAndAlignFromPlacement", "split", "$cf1ac5d9fe0e8206$export$be92b6f5f03c0fe9", "$cf1ac5d9fe0e8206$export$b688253958b8dfe7", "$cf1ac5d9fe0e8206$export$7c6e2c02157bb7d2", "$cf1ac5d9fe0e8206$export$21b07c8f274aebd5"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-popper\\dist\\packages\\react\\popper\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-popper\\dist\\packages\\react\\popper\\src\\Popper.tsx"], "sourcesContent": ["export {\n  createPopperScope,\n  //\n  <PERSON><PERSON>,\n  <PERSON>perAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n} from './Popper';\nexport type {\n  PopperProps,\n  PopperAnchorProps,\n  PopperContentProps,\n  PopperArrowProps,\n} from './Popper';\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  avoidCollisions?: boolean;\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      avoidCollisions = true,\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: autoUpdate,\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden' }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n              // hide the content if using the hide middleware and should be hidden\n              opacity: middlewareData.hide?.referenceHidden ? 0 : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = Radix.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "mappings": ";;;;;;;;;;ACyBA,MAAMA,yCAAY,GAAG,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAArB;AACA,MAAMC,yCAAa,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,CAAtB;AAKA;;;AAIA,MAAMC,iCAAW,GAAG,QAApB;AAGA,MAAM,CAACC,yCAAD,EAAsBC,uCAAtB,IAA2CC,yBAAkB,CAACH,iCAAD,CAAnE;AAMA,MAAM,CAACI,oCAAD,EAAiBC,sCAAjB,IAAqCJ,yCAAmB,CAAqBD,iCAArB,CAA9D;AAKA,MAAMM,yCAA6B,GAAIC,KAAD,IAAqC;EACzE,MAAM;mBAAEC,aAAF;cAAiBC;EAAA,CAAjB,GAA8BF,KAApC;EACA,MAAM,CAACG,MAAD,EAASC,SAAT,IAAsBC,eAAA,CAAkC,IAAlC,CAA5B;EACA,oBACEC,oBAAA,CAACT,oCAAD,EADF;IACkBU,KAAK,EAAEN,aAAvB;IAAsCE,MAAM,EAAEA,MAA9C;IAAsDK,cAAc,EAAEJ;GAAtE,EACGF,QADH,CADF;CAHF;AAUA;AAAAO,MAAA,CAAAC,MAAA,CAAAX,yCAAA;EAAAY,WAAA,EAAAlB;CAAA;AAEA;;;AAIA,MAAMmB,iCAAW,GAAG,cAApB;AAQA,MAAMC,wCAAY,gBAAGC,iBAAA,CACnB,CAACd,KAAD,EAAwCe,YAAxC,KAAyD;EACvD,MAAM;mBAAEd,aAAF;gBAAiBe,UAAjB;IAA6B,GAAGC;EAAH,CAA7B,GAAgDjB,KAAtD;EACA,MAAMkB,OAAO,GAAGpB,sCAAgB,CAACc,iCAAD,EAAcX,aAAd,CAAhC;EACA,MAAMkB,GAAG,GAAGC,aAAA,CAAkC,IAAlC,CAAZ;EACA,MAAMC,YAAY,GAAGC,sBAAe,CAACP,YAAD,EAAeI,GAAf,CAApC;EAEAI,gBAAA,CAAgB,MAAM;IACpB;IACA;IACA;IACAL,OAAO,CAACV,cAAR,CAAuB,CAAAQ,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAEQ,OAAZ,KAAuBL,GAAG,CAACK,OAAlD;GAJF,CAKC;EAED,OAAOR,UAAU,GAAG,IAAH,gBAAUV,oBAAA,CAACmB,gBAAD,CAAWC,GAAX,EAAAC,oCAAA,KAAmBV,WAAnB,EAA3B;IAA2DE,GAAG,EAAEE;GAArC,EAA3B;CAdiB,CAArB;AAkBA;AAAAZ,MAAA,CAAAC,MAAA,CAAAG,wCAAA;EAAAF,WAAA,EAAAC;CAAA;AAEA;;;AAIA,MAAMgB,kCAAY,GAAG,eAArB;AAUA,MAAM,CAACC,2CAAD,EAAwBC,uCAAxB,IACJpC,yCAAmB,CAA4BkC,kCAA5B,CADrB;AAoBA,MAAMG,wCAAa,gBAAGjB,iBAAA,CACpB,CAACd,KAAD,EAAyCe,YAAzC,KAA0D;EAAA,IAAAiB,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA;EACxD,MAAM;mBACJtC,aADI;IAEJuC,IAAI,GAAG,QAFH;IAGJC,UAAU,GAAG,CAHT;IAIJC,KAAK,GAAG,QAJJ;IAKJC,WAAW,GAAG,CALV;IAMJC,YAAY,GAAG,CANX;IAOJC,iBAAiB,GAAG,EAPhB;IAQJC,gBAAgB,EAAEC,oBAAoB,GAAG,CARrC;IASJC,MAAM,GAAG,SATL;IAUJC,gBAAgB,GAAG,KAVf;IAWJC,eAAe,GAAG,IAXd;cAYJC,QAZI;IAaJ,GAAGC;EAAH,CAbI,GAcFpD,KAdJ;EAgBA,MAAMkB,OAAO,GAAGpB,sCAAgB,CAAC8B,kCAAD,EAAe3B,aAAf,CAAhC;EAEA,MAAM,CAACoD,OAAD,EAAUC,UAAV,IAAwBjD,eAAA,CAAsC,IAAtC,CAA9B;EACA,MAAMgB,YAAY,GAAGC,sBAAe,CAACP,YAAD,EAAgBwC,IAAD,IAAUD,UAAU,CAACC,IAAD,CAAnC,CAApC;EAEA,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBpD,eAAA,CAAuC,IAAvC,CAA1B;EACA,MAAMqD,SAAS,GAAGC,cAAO,CAACH,KAAD,CAAzB;EACA,MAAMI,UAAU,IAAA5B,gBAAA,GAAG0B,SAAH,aAAGA,SAAH,uBAAGA,SAAS,CAAEG,KAAd,cAAA7B,gBAAA,cAAAA,gBAAA,GAAuB,CAAvC;EACA,MAAM8B,WAAW,IAAA7B,iBAAA,GAAGyB,SAAH,aAAGA,SAAH,uBAAGA,SAAS,CAAEK,MAAd,cAAA9B,iBAAA,cAAAA,iBAAA,GAAwB,CAAzC;EAEA,MAAM+B,gBAAgB,GAAIxB,IAAI,IAAIE,KAAK,KAAK,QAAV,GAAqB,MAAMA,KAA3B,GAAmC,EAAvC;EAE9B,MAAMI,gBAAgB,GACpB,OAAOC,oBAAP,KAAgC,QAAhC,GACIA,oBADJ,GAEI;IAAEkB,GAAG,EAAE,CAAP;IAAUC,KAAK,EAAE,CAAjB;IAAoBC,MAAM,EAAE,CAA5B;IAA+BC,IAAI,EAAE,CAArC;IAAwC,GAAGrB;GAHjD;EAKA,MAAMsB,QAAQ,GAAGC,KAAK,CAACC,OAAN,CAAc1B,iBAAd,IAAmCA,iBAAnC,GAAuD,CAACA,iBAAD,CAAxE;EACA,MAAM2B,qBAAqB,GAAGH,QAAQ,CAACI,MAAT,GAAkB,CAAhD;EAEA,MAAMC,qBAAqB,GAAG;IAC5BC,OAAO,EAAE7B,gBADmB;IAE5BuB,QAAQ,EAAEA,QAAQ,CAACO,MAAT,CAAgBC,+BAAhB,CAFkB;IAG5B;IACAC,WAAW,EAAEN;GAJf;EAOA,MAAM;UAAEO,IAAF;oBAAQC,cAAR;eAAwBC,SAAxB;kBAAmCC,YAAnC;oBAAiDC;EAAA,CAAjD,GAAoEC,kBAAW,CAAC;IACpF;IACAC,QAAQ,EAAE,OAF0E;IAGpFJ,SAAS,EAAEjB,gBAHyE;IAIpFsB,oBAAoB,EAAEC,iBAJ8D;IAKpFC,QAAQ,EAAE;MACRC,SAAS,EAAEvE,OAAO,CAACf;KAN+D;IAQpFuF,UAAU,EAAE,CACVC,aAAM,CAAC;MAAEC,QAAQ,EAAEnD,UAAU,GAAGqB,WAAzB;MAAsC+B,aAAa,EAAElD;KAAtD,CADI,EAEVO,eAAe,IACb4C,YAAK,CAAC;MACJF,QAAQ,EAAE,IADN;MAEJG,SAAS,EAAE,KAFP;MAGJC,OAAO,EAAEhD,MAAM,KAAK,SAAX,GAAuBiD,iBAAU,EAAjC,GAAsCC,SAH3C;MAIJ,GAAGxB;KAJA,CAHG,EASVxB,eAAe,IAAIiD,WAAI,CAAC;MAAE,GAAGzB;KAAN,CATb,EAUV0B,WAAI,CAAC;MACH,GAAG1B,qBADA;MAEH2B,KAAK,EAAEA,CAAC;kBAAEb,QAAF;eAAYc,KAAZ;wBAAmBC,cAAnB;yBAAmCC;MAAA,CAApC,KAA0D;QAC/D,MAAM;UAAE3C,KAAK,EAAE4C,WAAT;UAAsB1C,MAAM,EAAE2C;QAAR,CAAtB,GAA+CJ,KAAK,CAACb,SAA3D;QACA,MAAMkB,YAAY,GAAGnB,QAAQ,CAACoB,QAAT,CAAkBC,KAAvC;QACAF,YAAY,CAACG,WAAb,CAAyB,gCAAzB,EAA4D,GAAEP,cAAe,IAA7E;QACAI,YAAY,CAACG,WAAb,CAAyB,iCAAzB,EAA6D,GAAEN,eAAgB,IAA/E;QACAG,YAAY,CAACG,WAAb,CAAyB,6BAAzB,EAAyD,GAAEL,WAAY,IAAvE;QACAE,YAAY,CAACG,WAAb,CAAyB,8BAAzB,EAA0D,GAAEJ,YAAa,IAAzE;;KARA,CAVM,EAqBVlD,KAAK,IAAIuD,YAAe,CAAC;MAAEC,OAAO,EAAExD,KAAX;MAAkBmB,OAAO,EAAE/B;KAA5B,CArBd,EAsBVqE,qCAAe,CAAC;kBAAErD,UAAF;mBAAcE;KAAf,CAtBL,EAuBVb,gBAAgB,IAAIiE,WAAI,CAAC;MAAE7B,QAAQ,EAAE;KAAb,CAvBd;GARuE,CAArF;EAmCA,MAAM,CAAC8B,UAAD,EAAaC,WAAb,IAA4BC,kDAA4B,CAACpC,SAAD,CAA9D;EAEA,MAAMqC,YAAY,GAAGC,qBAAc,CAACpE,QAAD,CAAnC;EACAqE,sBAAe,CAAC,MAAM;IACpB,IAAItC,YAAJ,EACEoC,YAAY,SAAZ,IAAAA,YAAY,WAAZ,IAAAA,YAAY,EAAZ;GAFW,EAIZ,CAACpC,YAAD,EAAeoC,YAAf,CAJY,CAAf;EAMA,MAAMG,MAAM,IAAAvF,qBAAA,GAAGiD,cAAc,CAAC3B,KAAlB,cAAAtB,qBAAA,uBAAGA,qBAAA,CAAsBwF,CAArC;EACA,MAAMC,MAAM,IAAAxF,sBAAA,GAAGgD,cAAc,CAAC3B,KAAlB,cAAArB,sBAAA,uBAAGA,sBAAA,CAAsByF,CAArC;EACA,MAAMC,iBAAiB,GAAG,EAAAzF,sBAAA,GAAA+C,cAAc,CAAC3B,KAAf,cAAApB,sBAAA,uBAAAA,sBAAA,CAAsB0F,YAAtB,MAAuC,CAAjE;EAEA,MAAM,CAACC,aAAD,EAAgBC,gBAAhB,IAAoC3H,eAAA,EAA1C;EACAmH,sBAAe,CAAC,MAAM;IACpB,IAAInE,OAAJ,EAAa2E,gBAAgB,CAACC,MAAM,CAACC,gBAAP,CAAwB7E,OAAxB,EAAiC8E,MAAlC,CAAhB;GADA,EAEZ,CAAC9E,OAAD,CAFY,CAAf;EAIA,oBACE/C,oBADF;IAEIa,GAAG,EAAE4D,IAAI,CAACqD,WADZ;IAEE,qCAAkC,EAFpC;IAGEvB,KAAK,EAAE;MACL,GAAG7B,cADE;MAELqD,SAAS,EAAEnD,YAAY,GAAGF,cAAc,CAACqD,SAAlB,GAA8B,qBAFhD;MAEuE;MAC5EC,QAAQ,EAAE,aAHL;MAILH,MAAM,EAAEJ,aAJH;MAKL,CAAC,iCAAD,GAA4C,EAAA1F,qBAAA,GAC1C8C,cAAc,CAACoD,eAD2B,cAAAlG,qBAAA,uBAC1CA,qBAAA,CAAgCqF,CADU,GAAApF,sBAAA,GAE1C6C,cAAc,CAACoD,eAF2B,cAAAjG,sBAAA,uBAE1CA,sBAAA,CAAgCsF,CAFU,EAG1CY,IAH0C,CAGrC,GAHqC;KARhD,CAaE;IAAA;;IAGAC,GAAG,EAAEzI,KAAK,CAACyI;GAhBb,eAkBEnI,oBAAA,CAACuB,2CAAD,EAlBF;IAmBItB,KAAK,EAAEN,aADT;IAEEkH,UAAU,EAAEA,UAFd;IAGEuB,aAAa,EAAEjF,QAHjB;IAIEgE,MAAM,EAAEA,MAJV;IAKEE,MAAM,EAAEA,MALV;IAMEgB,eAAe,EAAEd;GANnB,eAQEvH,oBAAA,CAACmB,gBAAD,CAAWC,GAAX,EARFC,oCAAA;IASI,aAAWwF,UADb;IAEE,cAAYC;GAFd,EAGMhE,YAHN;IAIEjC,GAAG,EAAEE,YAJP;IAKEwF,KAAK,EAAE;MACL,GAAGzD,YAAY,CAACyD,KADX;MAEL;MACA;MACA+B,SAAS,EAAE,CAAC1D,YAAD,GAAgB,MAAhB,GAAyBgB,SAJ/B;MAKL;MACA2C,OAAO,EAAE,CAAAtG,oBAAA,GAAA4C,cAAc,CAAC2D,IAAf,cAAAvG,oBAAA,eAAAA,oBAAA,CAAqBwG,eAArB,GAAuC,CAAvC,GAA2C7C;;GAXxD,EARF,CAlBF,CADF;CAlGkB,CAAtB;AAiJA;AAAAzF,MAAA,CAAAC,MAAA,CAAAqB,wCAAA;EAAApB,WAAA,EAAAiB;CAAA;AAEA;;;AAIA,MAAMoH,gCAAU,GAAG,aAAnB;AAEA,MAAMC,mCAAiC,GAAG;EACxChF,GAAG,EAAE,QADmC;EAExCC,KAAK,EAAE,MAFiC;EAGxCC,MAAM,EAAE,KAHgC;EAIxCC,IAAI,EAAE;CAJR;AAWA,MAAM8E,yCAAW,gBAAGpI,iBAAA,CAAuD,SAASoI,yCAATC,CACzEnJ,KADyE,EAEzEe,YAFyE,EAGzE;EACA,MAAM;mBAAEd,aAAF;IAAiB,GAAGmJ;EAAH,CAAjB,GAAmCpJ,KAAzC;EACA,MAAMqJ,cAAc,GAAGvH,uCAAiB,CAACkH,gCAAD,EAAa/I,aAAb,CAAxC;EACA,MAAMqJ,QAAQ,GAAGL,mCAAa,CAACI,cAAc,CAAClC,UAAhB,CAA9B;EAEA,sBACE;IACA;IACA;IACA7G,oBAAA;MACEa,GAAG,EAAEkI,cAAc,CAACX,aADtB;MAEE7B,KAAK,EAAE;QACL0C,QAAQ,EAAE,UADL;QAELnF,IAAI,EAAEiF,cAAc,CAAC5B,MAFhB;QAGLxD,GAAG,EAAEoF,cAAc,CAAC1B,MAHf;QAIL,CAAC2B,QAAD,GAAY,CAJP;QAKLf,eAAe,EAAE;UACftE,GAAG,EAAE,EADU;UAEfC,KAAK,EAAE,KAFQ;UAGfC,MAAM,EAAE,UAHO;UAIfC,IAAI,EAAE;SAJS,CAKfiF,cAAc,CAAClC,UALA,CALZ;QAWLkB,SAAS,EAAE;UACTpE,GAAG,EAAE,kBADI;UAETC,KAAK,EAAE,gDAFE;UAGTC,MAAM,EAAG,gBAHA;UAITC,IAAI,EAAE;SAJG,CAKTiF,cAAc,CAAClC,UALN,CAXN;QAiBLqC,UAAU,EAAEH,cAAc,CAACV,eAAf,GAAiC,QAAjC,GAA4CzC;;KAnB5D,eAsBE5F,oBAAA,CAACmJ,WAAD,EAAA9H,oCAAA,KACMyH,UADN,EAtBF;MAwBIjI,GAAG,EAAEJ,YAFP;MAGE8F,KAAK,EAAE;QACL,GAAGuC,UAAU,CAACvC,KADT;QAEL;QACA6C,OAAO,EAAE;;KANb,EAtBF;EAsBE;CAlCc,CAApB;AA+CA;AAAAjJ,MAAA,CAAAC,MAAA,CAAAwI,yCAAA;EAAAvI,WAAA,EAAAqI;CAAA;AAEA;AAEA,SAASnE,+BAAT8E,CAAsBC,KAAtB,EAAmD;EACjD,OAAOA,KAAK,KAAK,IAAjB;;AAGF,MAAM3C,qCAAe,GAAI4C,OAAD,KAAuE;EAC7FC,IAAI,EAAE,iBADuF;WAE7FD,OAF6F;EAG7FE,EAAEA,CAACC,IAAD,EAAO;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACP,MAAM;iBAAEpF,SAAF;aAAaqB,KAAb;sBAAoBnB;IAAA,CAApB,GAAuC6E,IAA7C;IAEA,MAAMnC,iBAAiB,GAAG,EAAAoC,sBAAA,GAAA9E,cAAc,CAAC3B,KAAf,cAAAyG,sBAAA,uBAAAA,sBAAA,CAAsBnC,YAAtB,MAAuC,CAAjE;IACA,MAAMwC,aAAa,GAAGzC,iBAAtB;IACA,MAAMjE,UAAU,GAAG0G,aAAa,GAAG,CAAH,GAAOT,OAAO,CAACjG,UAA/C;IACA,MAAME,WAAW,GAAGwG,aAAa,GAAG,CAAH,GAAOT,OAAO,CAAC/F,WAAhD;IAEA,MAAM,CAACqD,UAAD,EAAaC,WAAb,IAA4BC,kDAA4B,CAACpC,SAAD,CAA9D;IACA,MAAMsF,YAAY,GAAG;MAAEC,KAAK,EAAE,IAAT;MAAeC,MAAM,EAAE,KAAvB;MAA8BC,GAAG,EAAE;KAAnC,CAA4CtD,WAA5C,CAArB;IAEA,MAAMuD,YAAY,GAAG,EAAAT,sBAAA,IAAAC,sBAAA,GAAChF,cAAc,CAAC3B,KAAhB,cAAA2G,sBAAA,uBAACA,sBAAA,CAAsBzC,CAAvB,cAAAwC,sBAAA,cAAAA,sBAAA,GAA4B,CAA5B,IAAiCtG,UAAU,GAAG,CAAnE;IACA,MAAMgH,YAAY,GAAG,EAAAR,sBAAA,IAAAC,sBAAA,GAAClF,cAAc,CAAC3B,KAAhB,cAAA6G,sBAAA,uBAACA,sBAAA,CAAsBzC,CAAvB,cAAAwC,sBAAA,cAAAA,sBAAA,GAA4B,CAA5B,IAAiCtG,WAAW,GAAG,CAApE;IAEA,IAAI4D,CAAC,GAAG,EAAR;IACA,IAAIE,CAAC,GAAG,EAAR;IAEA,IAAIT,UAAU,KAAK,QAAnB,EAA6B;MAC3BO,CAAC,GAAG4C,aAAa,GAAGC,YAAH,GAAmB,GAAEI,YAAa,IAAnD;MACA/C,CAAC,GAAI,GAAE,CAAC9D,WAAY,IAApB;KAFF,MAGO,IAAIqD,UAAU,KAAK,KAAnB,EAA0B;MAC/BO,CAAC,GAAG4C,aAAa,GAAGC,YAAH,GAAmB,GAAEI,YAAa,IAAnD;MACA/C,CAAC,GAAI,GAAEtB,KAAK,CAACM,QAAN,CAAe7C,MAAf,GAAwBD,WAAY,IAA3C;KAFK,MAGA,IAAIqD,UAAU,KAAK,OAAnB,EAA4B;MACjCO,CAAC,GAAI,GAAE,CAAC5D,WAAY,IAApB;MACA8D,CAAC,GAAG0C,aAAa,GAAGC,YAAH,GAAmB,GAAEK,YAAa,IAAnD;KAFK,MAGA,IAAIzD,UAAU,KAAK,MAAnB,EAA2B;MAChCO,CAAC,GAAI,GAAEpB,KAAK,CAACM,QAAN,CAAe/C,KAAf,GAAuBC,WAAY,IAA1C;MACA8D,CAAC,GAAG0C,aAAa,GAAGC,YAAH,GAAmB,GAAEK,YAAa,IAAnD;;IAEF,OAAO;MAAEZ,IAAI,EAAE;WAAEtC,CAAF;WAAKE;;KAApB;;CAjCoB;AAqCxB,SAASP,kDAATwD,CAAsC5F,SAAtC,EAA4D;EAC1D,MAAM,CAACzC,IAAD,EAAOE,KAAK,GAAG,QAAf,IAA2BuC,SAAS,CAAC6F,KAAV,CAAgB,GAAhB,CAAjC;EACA,OAAO,CAACtI,IAAD,EAAeE,KAAf,CAAP;;AAGF,MAAMqI,yCAAI,GAAGhL,yCAAb;AACA,MAAMiL,yCAAM,GAAGnK,wCAAf;AACA,MAAMoK,yCAAO,GAAGlJ,wCAAhB;AACA,MAAMmJ,yCAAK,GAAGhC,yCAAd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}