{"ast": null, "code": "import { assignRef } from './assignRef';\nimport { createCallbackRef } from './createRef';\n/**\n * Transforms one ref to another\n * @example\n * ```tsx\n * const ResizableWithRef = forwardRef((props, ref) =>\n *   <Resizable {...props} ref={transformRef(ref, i => i ? i.resizable : null)}/>\n * );\n * ```\n */\nexport function transformRef(ref, transformer) {\n  return createCallbackRef(function (value) {\n    return assignRef(ref, transformer(value));\n  });\n}", "map": {"version": 3, "names": ["assignRef", "createCallbackRef", "transformRef", "ref", "transformer", "value"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-callback-ref/dist/es2015/transformRef.js"], "sourcesContent": ["import { assignRef } from './assignRef';\nimport { createCallbackRef } from './createRef';\n/**\n * Transforms one ref to another\n * @example\n * ```tsx\n * const ResizableWithRef = forwardRef((props, ref) =>\n *   <Resizable {...props} ref={transformRef(ref, i => i ? i.resizable : null)}/>\n * );\n * ```\n */\nexport function transformRef(ref, transformer) {\n    return createCallbackRef(function (value) { return assignRef(ref, transformer(value)); });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,iBAAiB,QAAQ,aAAa;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,GAAG,EAAEC,WAAW,EAAE;EAC3C,OAAOH,iBAAiB,CAAC,UAAUI,KAAK,EAAE;IAAE,OAAOL,SAAS,CAACG,GAAG,EAAEC,WAAW,CAACC,KAAK,CAAC,CAAC;EAAE,CAAC,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}