{"ast": null, "code": "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n  var left = _a.left,\n    top = _a.top,\n    right = _a.right,\n    gap = _a.gap;\n  if (gapMode === void 0) {\n    gapMode = 'margin';\n  }\n  return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([allowRelative && \"position: relative \".concat(important, \";\"), gapMode === 'margin' && \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"), gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\")].filter(Boolean).join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n  var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n  return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n  React.useEffect(function () {\n    document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n    return function () {\n      var newCounter = getCurrentUseCounter() - 1;\n      if (newCounter <= 0) {\n        document.body.removeAttribute(lockAttribute);\n      } else {\n        document.body.setAttribute(lockAttribute, newCounter.toString());\n      }\n    };\n  }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n  var noRelative = _a.noRelative,\n    noImportant = _a.noImportant,\n    _b = _a.gapMode,\n    gapMode = _b === void 0 ? 'margin' : _b;\n  useLockAttribute();\n  /*\n   gap will be measured on every component mount\n   however it will be used only by the \"first\" invocation\n   due to singleton nature of <Style\n   */\n  var gap = React.useMemo(function () {\n    return getGapWidth(gapMode);\n  }, [gapMode]);\n  return React.createElement(Style, {\n    styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '')\n  });\n};", "map": {"version": 3, "names": ["React", "styleSingleton", "fullWidthClassName", "zeroRightClassName", "noScrollbarsClassName", "removedBarSizeVariable", "getGapWidth", "Style", "lockAttribute", "getStyles", "_a", "allowRelative", "gapMode", "important", "left", "top", "right", "gap", "concat", "filter", "Boolean", "join", "getCurrentUseCounter", "counter", "parseInt", "document", "body", "getAttribute", "isFinite", "useLockAttribute", "useEffect", "setAttribute", "toString", "newCounter", "removeAttribute", "RemoveScrollBar", "noRelative", "noImportant", "_b", "useMemo", "createElement", "styles"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/react-remove-scroll-bar/dist/es2015/component.js"], "sourcesContent": ["import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,QAAQ,aAAa;AACnH,SAASC,WAAW,QAAQ,SAAS;AACrC,IAAIC,KAAK,GAAGN,cAAc,CAAC,CAAC;AAC5B,OAAO,IAAIO,aAAa,GAAG,oBAAoB;AAC/C;AACA;AACA;AACA,IAAIC,SAAS,GAAG,SAAAA,CAAUC,EAAE,EAAEC,aAAa,EAAEC,OAAO,EAAEC,SAAS,EAAE;EAC7D,IAAIC,IAAI,GAAGJ,EAAE,CAACI,IAAI;IAAEC,GAAG,GAAGL,EAAE,CAACK,GAAG;IAAEC,KAAK,GAAGN,EAAE,CAACM,KAAK;IAAEC,GAAG,GAAGP,EAAE,CAACO,GAAG;EAChE,IAAIL,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,QAAQ;EAAE;EAC9C,OAAO,OAAO,CAACM,MAAM,CAACd,qBAAqB,EAAE,0BAA0B,CAAC,CAACc,MAAM,CAACL,SAAS,EAAE,uBAAuB,CAAC,CAACK,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAACV,aAAa,EAAE,4BAA4B,CAAC,CAACU,MAAM,CAACL,SAAS,EAAE,4CAA4C,CAAC,CAACK,MAAM,CAAC,CACnSP,aAAa,IAAI,qBAAqB,CAACO,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,EAC7DD,OAAO,KAAK,QAAQ,IAChB,sBAAsB,CAACM,MAAM,CAACJ,IAAI,EAAE,wBAAwB,CAAC,CAACI,MAAM,CAACH,GAAG,EAAE,0BAA0B,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gEAAgE,CAAC,CAACE,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,SAAS,CAAC,EACzOD,OAAO,KAAK,SAAS,IAAI,iBAAiB,CAACM,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,GAAG,CAAC,CACvF,CACIM,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAACH,MAAM,CAACf,kBAAkB,EAAE,iBAAiB,CAAC,CAACe,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAAChB,kBAAkB,EAAE,wBAAwB,CAAC,CAACgB,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC,CAACC,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAACf,kBAAkB,EAAE,IAAI,CAAC,CAACe,MAAM,CAACf,kBAAkB,EAAE,mBAAmB,CAAC,CAACe,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC,CAACK,MAAM,CAAChB,kBAAkB,EAAE,IAAI,CAAC,CAACgB,MAAM,CAAChB,kBAAkB,EAAE,0BAA0B,CAAC,CAACgB,MAAM,CAACL,SAAS,EAAE,qBAAqB,CAAC,CAACK,MAAM,CAACV,aAAa,EAAE,WAAW,CAAC,CAACU,MAAM,CAACb,sBAAsB,EAAE,IAAI,CAAC,CAACa,MAAM,CAACD,GAAG,EAAE,YAAY,CAAC;AAChlB,CAAC;AACD,IAAIK,oBAAoB,GAAG,SAAAA,CAAA,EAAY;EACnC,IAAIC,OAAO,GAAGC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACC,YAAY,CAACnB,aAAa,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;EAC5E,OAAOoB,QAAQ,CAACL,OAAO,CAAC,GAAGA,OAAO,GAAG,CAAC;AAC1C,CAAC;AACD,OAAO,IAAIM,gBAAgB,GAAG,SAAAA,CAAA,EAAY;EACtC7B,KAAK,CAAC8B,SAAS,CAAC,YAAY;IACxBL,QAAQ,CAACC,IAAI,CAACK,YAAY,CAACvB,aAAa,EAAE,CAACc,oBAAoB,CAAC,CAAC,GAAG,CAAC,EAAEU,QAAQ,CAAC,CAAC,CAAC;IAClF,OAAO,YAAY;MACf,IAAIC,UAAU,GAAGX,oBAAoB,CAAC,CAAC,GAAG,CAAC;MAC3C,IAAIW,UAAU,IAAI,CAAC,EAAE;QACjBR,QAAQ,CAACC,IAAI,CAACQ,eAAe,CAAC1B,aAAa,CAAC;MAChD,CAAC,MACI;QACDiB,QAAQ,CAACC,IAAI,CAACK,YAAY,CAACvB,aAAa,EAAEyB,UAAU,CAACD,QAAQ,CAAC,CAAC,CAAC;MACpE;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIG,eAAe,GAAG,SAAAA,CAAUzB,EAAE,EAAE;EACvC,IAAI0B,UAAU,GAAG1B,EAAE,CAAC0B,UAAU;IAAEC,WAAW,GAAG3B,EAAE,CAAC2B,WAAW;IAAEC,EAAE,GAAG5B,EAAE,CAACE,OAAO;IAAEA,OAAO,GAAG0B,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,EAAE;EACtHT,gBAAgB,CAAC,CAAC;EAClB;AACJ;AACA;AACA;AACA;EACI,IAAIZ,GAAG,GAAGjB,KAAK,CAACuC,OAAO,CAAC,YAAY;IAAE,OAAOjC,WAAW,CAACM,OAAO,CAAC;EAAE,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAChF,OAAOZ,KAAK,CAACwC,aAAa,CAACjC,KAAK,EAAE;IAAEkC,MAAM,EAAEhC,SAAS,CAACQ,GAAG,EAAE,CAACmB,UAAU,EAAExB,OAAO,EAAE,CAACyB,WAAW,GAAG,YAAY,GAAG,EAAE;EAAE,CAAC,CAAC;AACzH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}