{"ast": null, "code": "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n  var useStyle = styleHookSingleton();\n  var Sheet = function (_a) {\n    var styles = _a.styles,\n      dynamic = _a.dynamic;\n    useStyle(styles, dynamic);\n    return null;\n  };\n  return Sheet;\n};", "map": {"version": 3, "names": ["styleHookSingleton", "styleSingleton", "useStyle", "Sheet", "_a", "styles", "dynamic"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/react-style-singleton/dist/es2015/component.js"], "sourcesContent": ["import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,QAAQ;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAA,EAAY;EACpC,IAAIC,QAAQ,GAAGF,kBAAkB,CAAC,CAAC;EACnC,IAAIG,KAAK,GAAG,SAAAA,CAAUC,EAAE,EAAE;IACtB,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;MAAEC,OAAO,GAAGF,EAAE,CAACE,OAAO;IAC5CJ,QAAQ,CAACG,MAAM,EAAEC,OAAO,CAAC;IACzB,OAAO,IAAI;EACf,CAAC;EACD,OAAOH,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}