{"ast": null, "code": "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();", "map": {"version": 3, "names": ["createSidecarMedium", "effectCar"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/medium.js"], "sourcesContent": ["import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAa;AACjD,OAAO,IAAIC,SAAS,GAAGD,mBAAmB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}