{"ast": null, "code": "import { createContext as $3bkAK$createContext, useMemo as $3bkAK$useMemo, createElement as $3bkAK$createElement, useContext as $3bkAK$useContext } from \"react\";\nfunction $c512c27ab02ef895$export$fd42f52fd3ae1109(rootComponentName, defaultContext) {\n  const Context = /*#__PURE__*/$3bkAK$createContext(defaultContext);\n  function Provider(props) {\n    const {\n      children: children,\n      ...context\n    } = props; // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = $3bkAK$useMemo(() => context, Object.values(context));\n    return /*#__PURE__*/$3bkAK$createElement(Context.Provider, {\n      value: value\n    }, children);\n  }\n  function useContext(consumerName) {\n    const context = $3bkAK$useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext; // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext];\n}\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\nfunction $c512c27ab02ef895$export$50c7b4e9d9f19c1(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  /* -----------------------------------------------------------------------------------------------\n  * createContext\n  * ---------------------------------------------------------------------------------------------*/\n  function $c512c27ab02ef895$export$fd42f52fd3ae1109(rootComponentName, defaultContext) {\n    const BaseContext = /*#__PURE__*/$3bkAK$createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    function Provider(props) {\n      const {\n        scope: scope,\n        children: children,\n        ...context\n      } = props;\n      const Context = (scope === null || scope === void 0 ? void 0 : scope[scopeName][index]) || BaseContext; // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = $3bkAK$useMemo(() => context, Object.values(context));\n      return /*#__PURE__*/$3bkAK$createElement(Context.Provider, {\n        value: value\n      }, children);\n    }\n    function useContext(consumerName, scope) {\n      const Context = (scope === null || scope === void 0 ? void 0 : scope[scopeName][index]) || BaseContext;\n      const context = $3bkAK$useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext; // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext];\n  }\n  /* -----------------------------------------------------------------------------------------------\n  * createScope\n  * ---------------------------------------------------------------------------------------------*/\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map(defaultContext => {\n      return /*#__PURE__*/$3bkAK$createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = (scope === null || scope === void 0 ? void 0 : scope[scopeName]) || scopeContexts;\n      return $3bkAK$useMemo(() => ({\n        [`__scope${scopeName}`]: {\n          ...scope,\n          [scopeName]: contexts\n        }\n      }), [scope, contexts]);\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [$c512c27ab02ef895$export$fd42f52fd3ae1109, $c512c27ab02ef895$var$composeContextScopes(createScope, ...createContextScopeDeps)];\n}\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\nfunction $c512c27ab02ef895$var$composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope1 = () => {\n    const scopeHooks = scopes.map(createScope => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes1 = scopeHooks.reduce((nextScopes, {\n        useScope: useScope,\n        scopeName: scopeName\n      }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return {\n          ...nextScopes,\n          ...currentScope\n        };\n      }, {});\n      return $3bkAK$useMemo(() => ({\n        [`__scope${baseScope.scopeName}`]: nextScopes1\n      }), [nextScopes1]);\n    };\n  };\n  createScope1.scopeName = baseScope.scopeName;\n  return createScope1;\n}\nexport { $c512c27ab02ef895$export$fd42f52fd3ae1109 as createContext, $c512c27ab02ef895$export$50c7b4e9d9f19c1 as createContextScope };", "map": {"version": 3, "names": ["$c512c27ab02ef895$export$fd42f52fd3ae1109", "createContext", "rootComponentName", "defaultContext", "Context", "$3bkAK$createContext", "Provider", "props", "children", "context", "value", "$3bkAK$useMemo", "Object", "values", "$3bkAK$createElement", "useContext", "consumerName", "$3bkAK$useContext", "undefined", "Error", "displayName", "$c512c27ab02ef895$export$50c7b4e9d9f19c1", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "BaseContext", "index", "length", "scope", "createScope", "scopeContexts", "map", "useScope", "contexts", "$c512c27ab02ef895$var$composeContextScopes", "composeContextScopes", "scopes", "baseScope", "createScope1", "scopeHooks", "useComposedScopes", "overrideScopes", "nextScopes1", "reduce", "nextScopes", "scopeProps", "currentScope"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-context\\dist\\packages\\react\\context\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-context\\dist\\packages\\react\\context\\src\\createContext.tsx"], "sourcesContent": ["export { createContext, createContextScope } from './createContext';\nexport type { CreateScope, Scope } from './createContext';\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "mappings": ";ACEA,SAASA,yCAATC,CACEC,iBADF,EAEEC,cAFF,EAGE;EACA,MAAMC,OAAO,gBAAGC,oBAAA,CAAkDF,cAAlD,CAAhB;EAEA,SAASG,QAATA,CAAkBC,KAAlB,EAA2E;IACzE,MAAM;MATVC,QAAA,EASYA,QAAF;MAAY,GAAGC;IAAH,CAAZ,GAA2BF,KAAjC,CADyE,CAEzE;IACA;IACA,MAAMG,KAAK,GAAGC,cAAA,CAAc,MAAMF,OAApB,EAA6BG,MAAM,CAACC,MAAP,CAAcJ,OAAd,CAA7B,CAAd;IACA,oBAAOK,oBAAA,CAACV,OAAD,CAASE,QAAT,EAAP;MAAyBI,KAAK,EAAEA;KAAzB,EAAiCF,QAAjC,CAAP;;EAGF,SAASO,UAATA,CAAoBC,YAApB,EAA0C;IACxC,MAAMP,OAAO,GAAGQ,iBAAA,CAAiBb,OAAjB,CAAhB;IACA,IAAIK,OAAJ,EAAa,OAAOA,OAAP;IACb,IAAIN,cAAc,KAAKe,SAAvB,EAAkC,OAAOf,cAAP,CAHM,CAIxC;IACA,MAAM,IAAIgB,KAAJ,CAAW,KAAIH,YAAa,4BAA2Bd,iBAAkB,IAAzE,CAAN;;EAGFI,QAAQ,CAACc,WAAT,GAAuBlB,iBAAiB,GAAG,UAA3C;EACA,OAAO,CAACI,QAAD,EAAWS,UAAX,CAAP;;AAGF;;;AAWA,SAASM,wCAATC,CAA4BC,SAA5B,EAA+CC,sBAAqC,GAAG,EAAvF,EAA2F;EACzF,IAAIC,eAAsB,GAAG,EAA7B;EAEA;;;EAIA,SAASzB,yCAATC,CACEC,iBADF,EAEEC,cAFF,EAGE;IACA,MAAMuB,WAAW,gBAAGrB,oBAAA,CAAkDF,cAAlD,CAApB;IACA,MAAMwB,KAAK,GAAGF,eAAe,CAACG,MAA9B;IACAH,eAAe,GAAG,C,GAAIA,eAAJ,EAAqBtB,cAArB,CAAlB;IAEA,SAASG,QAATA,CACEC,KADF,EAEE;MACA,MAAM;QAzDZsB,KAAA,EAyDcA,KAAF;QAzDZrB,QAAA,EAyDqBA,QAAT;QAAmB,GAAGC;MAAH,CAAnB,GAAkCF,KAAxC;MACA,MAAMH,OAAO,GAAG,CAAAyB,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAGN,SAAH,CAAL,CAAmBI,KAAnB,MAA6BD,WAA7C,CAFA,CAGA;MACA;MACA,MAAMhB,KAAK,GAAGC,cAAA,CAAc,MAAMF,OAApB,EAA6BG,MAAM,CAACC,MAAP,CAAcJ,OAAd,CAA7B,CAAd;MACA,oBAAOK,oBAAA,CAACV,OAAD,CAASE,QAAT,EAAP;QAAyBI,KAAK,EAAEA;OAAzB,EAAiCF,QAAjC,CAAP;;IAGF,SAASO,UAATA,CAAoBC,YAApB,EAA0Ca,KAA1C,EAAsF;MACpF,MAAMzB,OAAO,GAAG,CAAAyB,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAGN,SAAH,CAAL,CAAmBI,KAAnB,MAA6BD,WAA7C;MACA,MAAMjB,OAAO,GAAGQ,iBAAA,CAAiBb,OAAjB,CAAhB;MACA,IAAIK,OAAJ,EAAa,OAAOA,OAAP;MACb,IAAIN,cAAc,KAAKe,SAAvB,EAAkC,OAAOf,cAAP,CAJkD,CAKpF;MACA,MAAM,IAAIgB,KAAJ,CAAW,KAAIH,YAAa,4BAA2Bd,iBAAkB,IAAzE,CAAN;;IAGFI,QAAQ,CAACc,WAAT,GAAuBlB,iBAAiB,GAAG,UAA3C;IACA,OAAO,CAACI,QAAD,EAAWS,UAAX,CAAP;;EAGF;;;EAIA,MAAMe,WAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,aAAa,GAAGN,eAAe,CAACO,GAAhB,CAAqB7B,cAAD,IAAoB;MAC5D,oBAAOE,oBAAA,CAAoBF,cAApB,CAAP;KADoB,CAAtB;IAGA,OAAO,SAAS8B,QAATA,CAAkBJ,KAAlB,EAAgC;MACrC,MAAMK,QAAQ,GAAG,CAAAL,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAGN,SAAH,CAAL,KAAsBQ,aAAvC;MACA,OAAOpB,cAAA,CACL,OAAO;QAAE,CAAE,UAASY,SAAU,EAArB,GAAyB;UAAE,GAAGM,KAAL;UAAY,CAACN,SAAD,GAAaW;;OAA3D,GACA,CAACL,KAAD,EAAQK,QAAR,CAFK,CAAP;KAFF;GAJF;EAaAJ,WAAW,CAACP,SAAZ,GAAwBA,SAAxB;EACA,OAAO,CAACvB,yCAAD,EAAgBmC,0CAAoB,CAACL,WAAD,KAAiBN,sBAAjB,CAApC,CAAP;;AAGF;;;AAIA,SAASW,0CAATC,CAA8B,GAAGC,MAAjC,EAAwD;EACtD,MAAMC,SAAS,GAAGD,MAAM,CAAC,CAAD,CAAxB;EACA,IAAIA,MAAM,CAACT,MAAP,KAAkB,CAAtB,EAAyB,OAAOU,SAAP;EAEzB,MAAMC,YAAwB,GAAGT,CAAA,KAAM;IACrC,MAAMU,UAAU,GAAGH,MAAM,CAACL,GAAP,CAAYF,WAAD,KAAkB;MAC9CG,QAAQ,EAAEH,WAAW,EADyB;MAE9CP,SAAS,EAAEO,WAAW,CAACP;KAFK,CAAX,CAAnB;IAKA,OAAO,SAASkB,iBAATA,CAA2BC,cAA3B,EAA2C;MAChD,MAAMC,WAAU,GAAGH,UAAU,CAACI,MAAX,CAAkB,CAACC,UAAD,EAAa;QAlHxDZ,QAAA,EAkH0DA,QAAF;QAlHxDV,SAAA,EAkHoEA;MAAA,CAAzB,KAAyC;QAC5E;QACA;QACA;QACA,MAAMuB,UAAU,GAAGb,QAAQ,CAACS,cAAD,CAA3B;QACA,MAAMK,YAAY,GAAGD,UAAU,CAAE,UAASvB,SAAU,EAArB,CAA/B;QACA,OAAO;UAAE,GAAGsB,UAAL;UAAiB,GAAGE;SAA3B;OANiB,EAOhB,EAPgB,CAAnB;MASA,OAAOpC,cAAA,CAAc,OAAO;QAAE,CAAE,UAAS2B,SAAS,CAACf,SAAU,EAA/B,GAAmCoB;OAA5C,GAA2D,CAACA,WAAD,CAAzE,CAAP;KAVF;GANF;EAoBAJ,YAAW,CAAChB,SAAZ,GAAwBe,SAAS,CAACf,SAAlC;EACA,OAAOgB,YAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}