{"ast": null, "code": "import $9IrjX$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $9IrjX$forwardRef, Children as $9IrjX$Children, isValidElement as $9IrjX$isValidElement, createElement as $9IrjX$createElement, cloneElement as $9IrjX$cloneElement, Fragment as $9IrjX$Fragment } from \"react\";\nimport { composeRefs as $9IrjX$composeRefs } from \"@radix-ui/react-compose-refs\";\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\nconst $5e63c961fc1ce211$export$8c6ed5c666ac1360 = /*#__PURE__*/$9IrjX$forwardRef((props, forwardedRef) => {\n  const {\n    children: children,\n    ...slotProps\n  } = props;\n  const childrenArray = $9IrjX$Children.toArray(children);\n  const slottable = childrenArray.find($5e63c961fc1ce211$var$isSlottable);\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map(child => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if ($9IrjX$Children.count(newElement) > 1) return $9IrjX$Children.only(null);\n        return /*#__PURE__*/$9IrjX$isValidElement(newElement) ? newElement.props.children : null;\n      } else return child;\n    });\n    return /*#__PURE__*/$9IrjX$createElement($5e63c961fc1ce211$var$SlotClone, $9IrjX$babelruntimehelpersesmextends({}, slotProps, {\n      ref: forwardedRef\n    }), /*#__PURE__*/$9IrjX$isValidElement(newElement) ? /*#__PURE__*/$9IrjX$cloneElement(newElement, undefined, newChildren) : null);\n  }\n  return /*#__PURE__*/$9IrjX$createElement($5e63c961fc1ce211$var$SlotClone, $9IrjX$babelruntimehelpersesmextends({}, slotProps, {\n    ref: forwardedRef\n  }), children);\n});\n$5e63c961fc1ce211$export$8c6ed5c666ac1360.displayName = 'Slot';\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\nconst $5e63c961fc1ce211$var$SlotClone = /*#__PURE__*/$9IrjX$forwardRef((props, forwardedRef) => {\n  const {\n    children: children,\n    ...slotProps\n  } = props;\n  if (/*#__PURE__*/$9IrjX$isValidElement(children)) return /*#__PURE__*/$9IrjX$cloneElement(children, {\n    ...$5e63c961fc1ce211$var$mergeProps(slotProps, children.props),\n    ref: forwardedRef ? $9IrjX$composeRefs(forwardedRef, children.ref) : children.ref\n  });\n  return $9IrjX$Children.count(children) > 1 ? $9IrjX$Children.only(null) : null;\n});\n$5e63c961fc1ce211$var$SlotClone.displayName = 'SlotClone';\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\nconst $5e63c961fc1ce211$export$d9f1ccf0bdb05d45 = ({\n  children: children\n}) => {\n  return /*#__PURE__*/$9IrjX$createElement($9IrjX$Fragment, null, children);\n};\n/* ---------------------------------------------------------------------------------------------- */\nfunction $5e63c961fc1ce211$var$isSlottable(child) {\n  return /*#__PURE__*/$9IrjX$isValidElement(child) && child.type === $5e63c961fc1ce211$export$d9f1ccf0bdb05d45;\n}\nfunction $5e63c961fc1ce211$var$mergeProps(slotProps, childProps) {\n  // all child props should override\n  const overrideProps = {\n    ...childProps\n  };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) overrideProps[propName] = (...args) => {\n        childPropValue(...args);\n        slotPropValue(...args);\n      };else if (slotPropValue) overrideProps[propName] = slotPropValue;\n    } else if (propName === 'style') overrideProps[propName] = {\n      ...slotPropValue,\n      ...childPropValue\n    };else if (propName === 'className') overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n  }\n  return {\n    ...slotProps,\n    ...overrideProps\n  };\n}\nconst $5e63c961fc1ce211$export$be92b6f5f03c0fe9 = $5e63c961fc1ce211$export$8c6ed5c666ac1360;\nexport { $5e63c961fc1ce211$export$8c6ed5c666ac1360 as Slot, $5e63c961fc1ce211$export$d9f1ccf0bdb05d45 as Slottable, $5e63c961fc1ce211$export$be92b6f5f03c0fe9 as Root };", "map": {"version": 3, "names": ["$5e63c961fc1ce211$export$8c6ed5c666ac1360", "$9IrjX$forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "$9IrjX$Children", "toArray", "slottable", "find", "$5e63c961fc1ce211$var$isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "count", "only", "$9IrjX$isValidElement", "$9IrjX$createElement", "$5e63c961fc1ce211$var$SlotClone", "$9IrjX$babelruntimehelpersesmextends", "ref", "$9IrjX$cloneElement", "undefined", "displayName", "$5e63c961fc1ce211$var$mergeProps", "$9IrjX$composeRefs", "$5e63c961fc1ce211$export$d9f1ccf0bdb05d45", "Slottable", "$9IrjX$Fragment", "isSlottable", "type", "mergeProps", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "<PERSON><PERSON><PERSON><PERSON>", "test", "args", "filter", "Boolean", "join", "$5e63c961fc1ce211$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-slot\\dist\\packages\\react\\slot\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-slot\\dist\\packages\\react\\slot\\src\\Slot.tsx"], "sourcesContent": ["export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './Slot';\nexport type { SlotProps } from './Slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n"], "mappings": ";;;;ACGA;;;AAQA,MAAMA,yCAAI,gBAAGC,iBAAA,CAAyC,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAC7E,MAAM;cAAEC,QAAF;IAAY,GAAGC;EAAH,CAAZ,GAA6BH,KAAnC;EACA,MAAMI,aAAa,GAAGC,eAAA,CAAeC,OAAf,CAAuBJ,QAAvB,CAAtB;EACA,MAAMK,SAAS,GAAGH,aAAa,CAACI,IAAd,CAAmBC,iCAAnB,CAAlB;EAEA,IAAIF,SAAJ,EAAe;IACb;IACA,MAAMG,UAAU,GAAGH,SAAS,CAACP,KAAV,CAAgBE,QAAnC;IAEA,MAAMS,WAAW,GAAGP,aAAa,CAACQ,GAAd,CAAmBC,KAAD,IAAW;MAC/C,IAAIA,KAAK,KAAKN,SAAd,EAAyB;QACvB;QACA;QACA,IAAIF,eAAA,CAAeS,KAAf,CAAqBJ,UAArB,IAAmC,CAAvC,EAA0C,OAAOL,eAAA,CAAeU,IAAf,CAAoB,IAApB,CAAP;QAC1C,OAAO,aAAAC,qBAAA,CAAqBN,UAArB,IACFA,UAAU,CAACV,KAAX,CAAiBE,QADf,GAEH,IAFJ;OAJF,MAQE,OAAOW,KAAP;KATgB,CAApB;IAaA,oBACEI,oBAAA,CAACC,+BAAD,EAAAC,oCAAA,KAAehB,SAAf,EADF;MAC4BiB,GAAG,EAAEnB;KAA/B,GACG,aAAAe,qBAAA,CAAqBN,UAArB,iBACGW,mBAAA,CAAmBX,UAAnB,EAA+BY,SAA/B,EAA0CX,WAA1C,CADH,GAEG,IAHN,CADF;;EASF,oBACEM,oBAAA,CAACC,+BAAD,EAAAC,oCAAA,KAAehB,SAAf,EADF;IAC4BiB,GAAG,EAAEnB;GAA/B,GACGC,QADH,CADF;CA/BW,CAAb;AAsCAJ,yCAAI,CAACyB,WAAL,GAAmB,MAAnB;AAEA;;;AAQA,MAAML,+BAAS,gBAAGnB,iBAAA,CAAsC,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAC/E,MAAM;cAAEC,QAAF;IAAY,GAAGC;EAAH,CAAZ,GAA6BH,KAAnC;EAEA,iBAAIgB,qBAAA,CAAqBd,QAArB,CAAJ,EACE,oBAAOmB,mBAAA,CAAmBnB,QAAnB,EAA6B;IAClC,GAAGsB,gCAAU,CAACrB,SAAD,EAAYD,QAAQ,CAACF,KAArB,CADqB;IAElCoB,GAAG,EAAEnB,YAAY,GAAGwB,kBAAW,CAACxB,YAAD,EAAgBC,QAAD,CAAkBkB,GAAjC,CAAd,GAAuDlB,QAAD,CAAkBkB;GAFpF,CAAP;EAMF,OAAOf,eAAA,CAAeS,KAAf,CAAqBZ,QAArB,IAAiC,CAAjC,GAAqCG,eAAA,CAAeU,IAAf,CAAoB,IAApB,CAArC,GAAiE,IAAxE;CAVgB,CAAlB;AAaAG,+BAAS,CAACK,WAAV,GAAwB,WAAxB;AAEA;;;AAIA,MAAMG,yCAAS,GAAGC,CAAC;YAAEzB;AAAA,CAAH,KAAiD;EACjE,oBAAOe,oBAAA,CAAAW,eAAA,QAAG1B,QAAH,CAAP;CADF;AAIA;AAIA,SAASO,iCAAToB,CAAqBhB,KAArB,EAA0E;EACxE,OAAO,aAAAG,qBAAA,CAAqBH,KAArB,KAA+BA,KAAK,CAACiB,IAAN,KAAeJ,yCAArD;;AAGF,SAASF,gCAATO,CAAoB5B,SAApB,EAAyC6B,UAAzC,EAA+D;EAC7D;EACA,MAAMC,aAAa,GAAG;IAAE,GAAGD;GAA3B;EAEA,KAAK,MAAME,QAAX,IAAuBF,UAAvB,EAAmC;IACjC,MAAMG,aAAa,GAAGhC,SAAS,CAAC+B,QAAD,CAA/B;IACA,MAAME,cAAc,GAAGJ,UAAU,CAACE,QAAD,CAAjC;IAEA,MAAMG,SAAS,GAAG,WAAWC,IAAX,CAAgBJ,QAAhB,CAAlB;IACA,IAAIG,SAAJ,EAAe;MACb;MACA,IAAIF,aAAa,IAAIC,cAArB,EACEH,aAAa,CAACC,QAAD,CAAb,GAA0B,CAAI,GAAAK,IAAJ,KAAwB;QAChDH,cAAc,IAAIG,IAAJ,CAAd;QACAJ,aAAa,IAAII,IAAJ,CAAb;OAFF,CAGC,KAGE,IAAIJ,aAAJ,EACHF,aAAa,CAACC,QAAD,CAAb,GAA0BC,aAA1B;KAVJ,MAcK,IAAID,QAAQ,KAAK,OAAjB,EACHD,aAAa,CAACC,QAAD,CAAb,GAA0B;MAAE,GAAGC,aAAL;MAAoB,GAAGC;KAAjD,CAA0B,KACrB,IAAIF,QAAQ,KAAK,WAAjB,EACLD,aAAa,CAACC,QAAD,CAAb,GAA0B,CAACC,aAAD,EAAgBC,cAAhB,EAAgCI,MAAhC,CAAuCC,OAAvC,EAAgDC,IAAhD,CAAqD,GAArD,CAA1B;;EAIJ,OAAO;IAAE,GAAGvC,SAAL;IAAgB,GAAG8B;GAA1B;;AAGF,MAAMU,yCAAI,GAAG7C,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}