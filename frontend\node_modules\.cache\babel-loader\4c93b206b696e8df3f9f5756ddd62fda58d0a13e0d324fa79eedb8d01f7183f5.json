{"ast": null, "code": "export var zeroGap = {\n  left: 0,\n  top: 0,\n  right: 0,\n  gap: 0\n};\nvar parse = function (x) {\n  return parseInt(x || '', 10) || 0;\n};\nvar getOffset = function (gapMode) {\n  var cs = window.getComputedStyle(document.body);\n  var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n  var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n  var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n  return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n  if (gapMode === void 0) {\n    gapMode = 'margin';\n  }\n  if (typeof window === 'undefined') {\n    return zeroGap;\n  }\n  var offsets = getOffset(gapMode);\n  var documentWidth = document.documentElement.clientWidth;\n  var windowWidth = window.innerWidth;\n  return {\n    left: offsets[0],\n    top: offsets[1],\n    right: offsets[2],\n    gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0])\n  };\n};", "map": {"version": 3, "names": ["zeroGap", "left", "top", "right", "gap", "parse", "x", "parseInt", "getOffset", "gapMode", "cs", "window", "getComputedStyle", "document", "body", "getGapWidth", "offsets", "documentWidth", "documentElement", "clientWidth", "windowWidth", "innerWidth", "Math", "max"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/react-remove-scroll-bar/dist/es2015/utils.js"], "sourcesContent": ["export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACjBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE;AACT,CAAC;AACD,IAAIC,KAAK,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAOC,QAAQ,CAACD,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC;AAAE,CAAC;AAC/D,IAAIE,SAAS,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAC/B,IAAIC,EAAE,GAAGC,MAAM,CAACC,gBAAgB,CAACC,QAAQ,CAACC,IAAI,CAAC;EAC/C,IAAIb,IAAI,GAAGS,EAAE,CAACD,OAAO,KAAK,SAAS,GAAG,aAAa,GAAG,YAAY,CAAC;EACnE,IAAIP,GAAG,GAAGQ,EAAE,CAACD,OAAO,KAAK,SAAS,GAAG,YAAY,GAAG,WAAW,CAAC;EAChE,IAAIN,KAAK,GAAGO,EAAE,CAACD,OAAO,KAAK,SAAS,GAAG,cAAc,GAAG,aAAa,CAAC;EACtE,OAAO,CAACJ,KAAK,CAACJ,IAAI,CAAC,EAAEI,KAAK,CAACH,GAAG,CAAC,EAAEG,KAAK,CAACF,KAAK,CAAC,CAAC;AAClD,CAAC;AACD,OAAO,IAAIY,WAAW,GAAG,SAAAA,CAAUN,OAAO,EAAE;EACxC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,QAAQ;EAAE;EAC9C,IAAI,OAAOE,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOX,OAAO;EAClB;EACA,IAAIgB,OAAO,GAAGR,SAAS,CAACC,OAAO,CAAC;EAChC,IAAIQ,aAAa,GAAGJ,QAAQ,CAACK,eAAe,CAACC,WAAW;EACxD,IAAIC,WAAW,GAAGT,MAAM,CAACU,UAAU;EACnC,OAAO;IACHpB,IAAI,EAAEe,OAAO,CAAC,CAAC,CAAC;IAChBd,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC;IACfb,KAAK,EAAEa,OAAO,CAAC,CAAC,CAAC;IACjBZ,GAAG,EAAEkB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,WAAW,GAAGH,aAAa,GAAGD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EAC1E,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}