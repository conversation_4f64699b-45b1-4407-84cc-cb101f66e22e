{"ast": null, "code": "import { useEffect as $1wErz$useEffect } from \"react\";\n\n/** Number of components which have requested interest to have focus guards */\nlet $3db38b7d1fb3fe6a$var$count = 0;\nfunction $3db38b7d1fb3fe6a$export$ac5b58043b79449b(props) {\n  $3db38b7d1fb3fe6a$export$b7ece24a22aeda8c();\n  return props.children;\n}\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction $3db38b7d1fb3fe6a$export$b7ece24a22aeda8c() {\n  $1wErz$useEffect(() => {\n    var _edgeGuards$, _edgeGuards$2;\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', (_edgeGuards$ = edgeGuards[0]) !== null && _edgeGuards$ !== void 0 ? _edgeGuards$ : $3db38b7d1fb3fe6a$var$createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', (_edgeGuards$2 = edgeGuards[1]) !== null && _edgeGuards$2 !== void 0 ? _edgeGuards$2 : $3db38b7d1fb3fe6a$var$createFocusGuard());\n    $3db38b7d1fb3fe6a$var$count++;\n    return () => {\n      if ($3db38b7d1fb3fe6a$var$count === 1) document.querySelectorAll('[data-radix-focus-guard]').forEach(node => node.remove());\n      $3db38b7d1fb3fe6a$var$count--;\n    };\n  }, []);\n}\nfunction $3db38b7d1fb3fe6a$var$createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\nconst $3db38b7d1fb3fe6a$export$be92b6f5f03c0fe9 = $3db38b7d1fb3fe6a$export$ac5b58043b79449b;\nexport { $3db38b7d1fb3fe6a$export$ac5b58043b79449b as FocusGuards, $3db38b7d1fb3fe6a$export$be92b6f5f03c0fe9 as Root, $3db38b7d1fb3fe6a$export$b7ece24a22aeda8c as useFocusGuards };", "map": {"version": 3, "names": ["$3db38b7d1fb3fe6a$var$count", "$3db38b7d1fb3fe6a$export$ac5b58043b79449b", "FocusGuards", "props", "$3db38b7d1fb3fe6a$export$b7ece24a22aeda8c", "children", "useFocusGuards", "$1wErz$useEffect", "_edgeGuards$", "_edgeGuards$2", "edgeGuards", "document", "querySelectorAll", "body", "insertAdjacentElement", "$3db38b7d1fb3fe6a$var$createFocusGuard", "for<PERSON>ach", "node", "remove", "createFocusGuard", "element", "createElement", "setAttribute", "tabIndex", "style", "cssText", "$3db38b7d1fb3fe6a$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-focus-guards\\dist\\packages\\react\\focus-guards\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-focus-guards\\dist\\packages\\react\\focus-guards\\src\\FocusGuards.tsx"], "sourcesContent": ["export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n"], "mappings": ";;ACEA;AACA,IAAIA,2BAAK,GAAG,CAAZ;AAEA,SAASC,yCAATC,CAAqBC,KAArB,EAAiC;EAC/BC,yCAAc,EAAd;EACA,OAAOD,KAAK,CAACE,QAAb;;AAGF;;;;AAIA,SAASD,yCAATE,CAAA,EAA0B;EACxBC,gBAAA,CAAgB,MAAM;IAAA,IAAAC,YAAA,EAAAC,aAAA;IACpB,MAAMC,UAAU,GAAGC,QAAQ,CAACC,gBAAT,CAA0B,0BAA1B,CAAnB;IACAD,QAAQ,CAACE,IAAT,CAAcC,qBAAd,CAAoC,YAApC,GAAAN,YAAA,GAAkDE,UAAU,CAAC,CAAD,CAA5D,cAAAF,YAAA,cAAAA,YAAA,GAAmEO,sCAAgB,EAAnF;IACAJ,QAAQ,CAACE,IAAT,CAAcC,qBAAd,CAAoC,WAApC,GAAAL,aAAA,GAAiDC,UAAU,CAAC,CAAD,CAA3D,cAAAD,aAAA,cAAAA,aAAA,GAAkEM,sCAAgB,EAAlF;IACAf,2BAAK,EAAL;IAEA,OAAO,MAAM;MACX,IAAIA,2BAAK,KAAK,CAAd,EACEW,QAAQ,CAACC,gBAAT,CAA0B,0BAA1B,EAAsDI,OAAtD,CAA+DC,IAAD,IAAUA,IAAI,CAACC,MAAL,EAAxE;MAEFlB,2BAAK,EAAL;KAJF;GANF,EAYG,EAZH,CAYC;;AAGH,SAASe,sCAATI,CAAA,EAA4B;EAC1B,MAAMC,OAAO,GAAGT,QAAQ,CAACU,aAAT,CAAuB,MAAvB,CAAhB;EACAD,OAAO,CAACE,YAAR,CAAqB,wBAArB,EAA+C,EAA/C;EACAF,OAAO,CAACG,QAAR,GAAmB,CAAnB;EACAH,OAAO,CAACI,KAAR,CAAcC,OAAd,GAAwB,kEAAxB;EACA,OAAOL,OAAP;;AAGF,MAAMM,yCAAI,GAAGzB,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}