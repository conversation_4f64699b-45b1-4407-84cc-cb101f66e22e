{"ast": null, "code": "var currentNonce;\nexport var setNonce = function (nonce) {\n  currentNonce = nonce;\n};\nexport var getNonce = function () {\n  if (currentNonce) {\n    return currentNonce;\n  }\n  if (typeof __webpack_nonce__ !== 'undefined') {\n    return __webpack_nonce__;\n  }\n  return undefined;\n};", "map": {"version": 3, "names": ["currentNonce", "setNonce", "nonce", "getNonce", "__webpack_nonce__", "undefined"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/get-nonce/dist/es2015/index.js"], "sourcesContent": ["var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n"], "mappings": "AAAA,IAAIA,YAAY;AAChB,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACnCF,YAAY,GAAGE,KAAK;AACxB,CAAC;AACD,OAAO,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAY;EAC9B,IAAIH,YAAY,EAAE;IACd,OAAOA,YAAY;EACvB;EACA,IAAI,OAAOI,iBAAiB,KAAK,WAAW,EAAE;IAC1C,OAAOA,iBAAiB;EAC5B;EACA,OAAOC,SAAS;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}