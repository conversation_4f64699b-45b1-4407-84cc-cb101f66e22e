{"ast": null, "code": "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n  var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n    return refs.forEach(function (ref) {\n      return assignRef(ref, newValue);\n    });\n  });\n  // handle refs changes - added or removed\n  useIsomorphicLayoutEffect(function () {\n    var oldValue = currentValues.get(callbackRef);\n    if (oldValue) {\n      var prevRefs_1 = new Set(oldValue);\n      var nextRefs_1 = new Set(refs);\n      var current_1 = callbackRef.current;\n      prevRefs_1.forEach(function (ref) {\n        if (!nextRefs_1.has(ref)) {\n          assignRef(ref, null);\n        }\n      });\n      nextRefs_1.forEach(function (ref) {\n        if (!prevRefs_1.has(ref)) {\n          assignRef(ref, current_1);\n        }\n      });\n    }\n    currentValues.set(callbackRef, refs);\n  }, [refs]);\n  return callbackRef;\n}", "map": {"version": 3, "names": ["React", "assignRef", "useCallbackRef", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "useEffect", "currentV<PERSON>ues", "WeakMap", "useMergeRefs", "refs", "defaultValue", "callback<PERSON><PERSON>", "newValue", "for<PERSON>ach", "ref", "oldValue", "get", "prevRefs_1", "Set", "nextRefs_1", "current_1", "current", "has", "set"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-callback-ref/dist/es2015/useMergeRef.js"], "sourcesContent": ["import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,UAAU;AACzC,IAAIC,yBAAyB,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGJ,KAAK,CAACK,eAAe,GAAGL,KAAK,CAACM,SAAS;AACvG,IAAIC,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAC7C,IAAIC,WAAW,GAAGV,cAAc,CAACS,YAAY,IAAI,IAAI,EAAE,UAAUE,QAAQ,EAAE;IACvE,OAAOH,IAAI,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOd,SAAS,CAACc,GAAG,EAAEF,QAAQ,CAAC;IAAE,CAAC,CAAC;EAC5E,CAAC,CAAC;EACF;EACAV,yBAAyB,CAAC,YAAY;IAClC,IAAIa,QAAQ,GAAGT,aAAa,CAACU,GAAG,CAACL,WAAW,CAAC;IAC7C,IAAII,QAAQ,EAAE;MACV,IAAIE,UAAU,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAAC;MAClC,IAAII,UAAU,GAAG,IAAID,GAAG,CAACT,IAAI,CAAC;MAC9B,IAAIW,SAAS,GAAGT,WAAW,CAACU,OAAO;MACnCJ,UAAU,CAACJ,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC9B,IAAI,CAACK,UAAU,CAACG,GAAG,CAACR,GAAG,CAAC,EAAE;UACtBd,SAAS,CAACc,GAAG,EAAE,IAAI,CAAC;QACxB;MACJ,CAAC,CAAC;MACFK,UAAU,CAACN,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC9B,IAAI,CAACG,UAAU,CAACK,GAAG,CAACR,GAAG,CAAC,EAAE;UACtBd,SAAS,CAACc,GAAG,EAAEM,SAAS,CAAC;QAC7B;MACJ,CAAC,CAAC;IACN;IACAd,aAAa,CAACiB,GAAG,CAACZ,WAAW,EAAEF,IAAI,CAAC;EACxC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EACV,OAAOE,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}