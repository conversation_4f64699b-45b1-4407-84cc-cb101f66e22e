{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ScissorsSquare = createLucideIcon(\"ScissorsSquare\", [[\"rect\", {\n  width: \"20\",\n  height: \"20\",\n  x: \"2\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1btzen\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"14cg06\"\n}], [\"path\", {\n  d: \"M9.414 9.414 12 12\",\n  key: \"qz4lzr\"\n}], [\"path\", {\n  d: \"M14.8 14.8 18 18\",\n  key: \"11flf1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1acxsx\"\n}], [\"path\", {\n  d: \"m18 6-8.586 8.586\",\n  key: \"11kzk1\"\n}]]);\nexport { ScissorsSquare as default };", "map": {"version": 3, "names": ["ScissorsSquare", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "cx", "cy", "r", "d"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\scissors-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ScissorsSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHg9IjIiIHk9IjIiIHJ4PSIyIiAvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSI4IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik05LjQxNCA5LjQxNCAxMiAxMiIgLz4KICA8cGF0aCBkPSJNMTQuOCAxNC44IDE4IDE4IiAvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSIxNiIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMTggNi04LjU4NiA4LjU4NiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/scissors-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ScissorsSquare = createLucideIcon('ScissorsSquare', [\n  [\n    'rect',\n    { width: '20', height: '20', x: '2', y: '2', rx: '2', key: '1btzen' },\n  ],\n  ['circle', { cx: '8', cy: '8', r: '2', key: '14cg06' }],\n  ['path', { d: 'M9.414 9.414 12 12', key: 'qz4lzr' }],\n  ['path', { d: 'M14.8 14.8 18 18', key: '11flf1' }],\n  ['circle', { cx: '8', cy: '16', r: '2', key: '1acxsx' }],\n  ['path', { d: 'm18 6-8.586 8.586', key: '11kzk1' }],\n]);\n\nexport default ScissorsSquare;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEI,CAAA,EAAG,oBAAsB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAEI,CAAA,EAAG,kBAAoB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEI,CAAA,EAAG,mBAAqB;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}