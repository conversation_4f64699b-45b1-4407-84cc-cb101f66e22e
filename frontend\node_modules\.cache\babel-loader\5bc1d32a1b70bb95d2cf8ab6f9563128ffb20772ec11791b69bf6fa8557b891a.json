{"ast": null, "code": "var IMPORTANT_MODIFIER = '!';\nfunction createSplitModifiers(config) {\n  var separator = config.separator || ':';\n  var isSeparatorSingleCharacter = separator.length === 1;\n  var firstSeparatorCharacter = separator[0];\n  var separatorLength = separator.length;\n  // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  return function splitModifiers(className) {\n    var modifiers = [];\n    var bracketDepth = 0;\n    var modifierStart = 0;\n    var postfixModifierPosition;\n    for (var index = 0; index < className.length; index++) {\n      var currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    var baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    var hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    var baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    var maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers: modifiers,\n      hasImportantModifier: hasImportantModifier,\n      baseClassName: baseClassName,\n      maybePostfixModifierPosition: maybePostfixModifierPosition\n    };\n  };\n}\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nfunction sortModifiers(modifiers) {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  var sortedModifiers = [];\n  var unsortedModifiers = [];\n  modifiers.forEach(function (modifier) {\n    var isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push.apply(sortedModifiers, unsortedModifiers.sort().concat([modifier]));\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push.apply(sortedModifiers, unsortedModifiers.sort());\n  return sortedModifiers;\n}\nexport { IMPORTANT_MODIFIER, createSplitModifiers, sortModifiers };", "map": {"version": 3, "names": ["IMPORTANT_MODIFIER", "createSplitModifiers", "config", "separator", "isSeparatorSingleCharacter", "length", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "splitModifiers", "className", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "slice", "push", "baseClassNameWithImportantModifier", "substring", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "undefined", "sortModifiers", "sortedModifiers", "unsortedModifiers", "for<PERSON>ach", "modifier", "isArbitraryVariant", "apply", "sort", "concat"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\modifier-utils.ts"], "sourcesContent": ["import { Config } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport function createSplitModifiers(config: Config) {\n    const separator = config.separator || ':'\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className: string) {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport function sortModifiers(modifiers: string[]) {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n"], "mappings": "AAEO,IAAMA,kBAAkB,GAAG;AAE5B,SAAUC,oBAAoBA,CAACC,MAAc;EAC/C,IAAMC,SAAS,GAAGD,MAAM,CAACC,SAAS,IAAI,GAAG;EACzC,IAAMC,0BAA0B,GAAGD,SAAS,CAACE,MAAM,KAAK,CAAC;EACzD,IAAMC,uBAAuB,GAAGH,SAAS,CAAC,CAAC,CAAC;EAC5C,IAAMI,eAAe,GAAGJ,SAAS,CAACE,MAAM;EAExC;EACA,OAAO,SAASG,cAAcA,CAACC,SAAiB;IAC5C,IAAMC,SAAS,GAAG,EAAE;IAEpB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,uBAA2C;IAE/C,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,SAAS,CAACJ,MAAM,EAAES,KAAK,EAAE,EAAE;MACnD,IAAIC,gBAAgB,GAAGN,SAAS,CAACK,KAAK,CAAC;MAEvC,IAAIH,YAAY,KAAK,CAAC,EAAE;QACpB,IACII,gBAAgB,KAAKT,uBAAuB,KAC3CF,0BAA0B,IACvBK,SAAS,CAACO,KAAK,CAACF,KAAK,EAAEA,KAAK,GAAGP,eAAe,CAAC,KAAKJ,SAAS,CAAC,EACpE;UACEO,SAAS,CAACO,IAAI,CAACR,SAAS,CAACO,KAAK,CAACJ,aAAa,EAAEE,KAAK,CAAC,CAAC;UACrDF,aAAa,GAAGE,KAAK,GAAGP,eAAe;UACvC;QACH;QAED,IAAIQ,gBAAgB,KAAK,GAAG,EAAE;UAC1BF,uBAAuB,GAAGC,KAAK;UAC/B;QACH;MACJ;MAED,IAAIC,gBAAgB,KAAK,GAAG,EAAE;QAC1BJ,YAAY,EAAE;MACjB,OAAM,IAAII,gBAAgB,KAAK,GAAG,EAAE;QACjCJ,YAAY,EAAE;MACjB;IACJ;IAED,IAAMO,kCAAkC,GACpCR,SAAS,CAACL,MAAM,KAAK,CAAC,GAAGI,SAAS,GAAGA,SAAS,CAACU,SAAS,CAACP,aAAa,CAAC;IAC3E,IAAMQ,oBAAoB,GACtBF,kCAAkC,CAACG,UAAU,CAACrB,kBAAkB,CAAC;IACrE,IAAMsB,aAAa,GAAGF,oBAAoB,GACpCF,kCAAkC,CAACC,SAAS,CAAC,CAAC,CAAC,GAC/CD,kCAAkC;IAExC,IAAMK,4BAA4B,GAC9BV,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAa,GAC5DC,uBAAuB,GAAGD,aAAa,GACvCY,SAAS;IAEnB,OAAO;MACHd,SAAS,EAATA,SAAS;MACTU,oBAAoB,EAApBA,oBAAoB;MACpBE,aAAa,EAAbA,aAAa;MACbC,4BAA4B,EAA5BA;KACH;GACJ;AACL;AAEA;;;;AAIG;AACG,SAAUE,aAAaA,CAACf,SAAmB;EAC7C,IAAIA,SAAS,CAACL,MAAM,IAAI,CAAC,EAAE;IACvB,OAAOK,SAAS;EACnB;EAED,IAAMgB,eAAe,GAAa,EAAE;EACpC,IAAIC,iBAAiB,GAAa,EAAE;EAEpCjB,SAAS,CAACkB,OAAO,CAAC,UAACC,QAAQ,EAAI;IAC3B,IAAMC,kBAAkB,GAAGD,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG;IAE9C,IAAIC,kBAAkB,EAAE;MACpBJ,eAAe,CAACT,IAAI,CAAAc,KAAA,CAApBL,eAAe,EAASC,iBAAiB,CAACK,IAAI,EAAE,CAAEC,MAAA,EAAAJ,QAAQ,CAAC;MAC3DF,iBAAiB,GAAG,EAAE;IACzB,OAAM;MACHA,iBAAiB,CAACV,IAAI,CAACY,QAAQ,CAAC;IACnC;EACL,CAAC,CAAC;EAEFH,eAAe,CAACT,IAAI,CAApBc,KAAA,CAAAL,eAAe,EAASC,iBAAiB,CAACK,IAAI,EAAE,CAAC;EAEjD,OAAON,eAAe;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}