{"ast": null, "code": "import $6vYhU$react from \"react\";\nimport { createContextScope as $6vYhU$createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs as $6vYhU$useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Slot as $6vYhU$Slot } from \"@radix-ui/react-slot\";\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\nfunction $e02a7d9cb1dc128c$export$c74125a8e3af6bb2(name) {\n  /* -----------------------------------------------------------------------------------------------\n  * CollectionProvider\n  * ---------------------------------------------------------------------------------------------*/\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = $6vYhU$createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n    collectionRef: {\n      current: null\n    },\n    itemMap: new Map()\n  });\n  const CollectionProvider = props => {\n    const {\n      scope: scope,\n      children: children\n    } = props;\n    const ref = $6vYhU$react.useRef(null);\n    const itemMap = $6vYhU$react.useRef(new Map()).current;\n    return /*#__PURE__*/$6vYhU$react.createElement(CollectionProviderImpl, {\n      scope: scope,\n      itemMap: itemMap,\n      collectionRef: ref\n    }, children);\n  };\n  /*#__PURE__*/\n  Object.assign(CollectionProvider, {\n    displayName: PROVIDER_NAME\n  });\n  /* -----------------------------------------------------------------------------------------------\n  * CollectionSlot\n  * ---------------------------------------------------------------------------------------------*/\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n  const CollectionSlot = /*#__PURE__*/$6vYhU$react.forwardRef((props, forwardedRef) => {\n    const {\n      scope: scope,\n      children: children\n    } = props;\n    const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n    const composedRefs = $6vYhU$useComposedRefs(forwardedRef, context.collectionRef);\n    return /*#__PURE__*/$6vYhU$react.createElement($6vYhU$Slot, {\n      ref: composedRefs\n    }, children);\n  });\n  /*#__PURE__*/\n  Object.assign(CollectionSlot, {\n    displayName: COLLECTION_SLOT_NAME\n  });\n  /* -----------------------------------------------------------------------------------------------\n  * CollectionItem\n  * ---------------------------------------------------------------------------------------------*/\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n  const CollectionItemSlot = /*#__PURE__*/$6vYhU$react.forwardRef((props, forwardedRef) => {\n    const {\n      scope: scope,\n      children: children,\n      ...itemData\n    } = props;\n    const ref = $6vYhU$react.useRef(null);\n    const composedRefs = $6vYhU$useComposedRefs(forwardedRef, ref);\n    const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n    $6vYhU$react.useEffect(() => {\n      context.itemMap.set(ref, {\n        ref: ref,\n        ...itemData\n      });\n      return () => void context.itemMap.delete(ref);\n    });\n    return /*#__PURE__*/$6vYhU$react.createElement($6vYhU$Slot, {\n      [ITEM_DATA_ATTR]: '',\n      ref: composedRefs\n    }, children);\n  });\n  /*#__PURE__*/\n  Object.assign(CollectionItemSlot, {\n    displayName: ITEM_SLOT_NAME\n  });\n  /* -----------------------------------------------------------------------------------------------\n  * useCollection\n  * ---------------------------------------------------------------------------------------------*/\n  function useCollection(scope) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n    const getItems = $6vYhU$react.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort((a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [{\n    Provider: CollectionProvider,\n    Slot: CollectionSlot,\n    ItemSlot: CollectionItemSlot\n  }, useCollection, createCollectionScope];\n}\nexport { $e02a7d9cb1dc128c$export$c74125a8e3af6bb2 as createCollection };", "map": {"version": 3, "names": ["$e02a7d9cb1dc128c$export$c74125a8e3af6bb2", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "$6vYhU$createContextScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "Map", "CollectionProvider", "props", "scope", "children", "ref", "$6vYhU$react", "useRef", "createElement", "Object", "assign", "displayName", "COLLECTION_SLOT_NAME", "CollectionSlot", "forwardRef", "forwardedRef", "context", "composedRefs", "$6vYhU$useComposedRefs", "$6vYhU$Slot", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "useCollection", "getItems", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "orderedItems", "sort", "a", "b", "indexOf", "Provider", "Slot", "ItemSlot"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-collection\\dist\\packages\\react\\collection\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-collection\\dist\\packages\\react\\collection\\src\\Collection.tsx"], "sourcesContent": ["export { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\ntype SlotProps = Radix.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n"], "mappings": ";;;;;ACaA;AACA;AACA;AACA;AAEA,SAASA,yCAATC,CAA0EC,IAA1E,EAAwF;EACtF;;;EAIA,MAAMC,aAAa,GAAGD,IAAI,GAAG,oBAA7B;EACA,MAAM,CAACE,uBAAD,EAA0BC,qBAA1B,IAAmDC,yBAAkB,CAACH,aAAD,CAA3E;EAOA,MAAM,CAACI,sBAAD,EAAyBC,oBAAzB,IAAiDJ,uBAAuB,CAC5ED,aAD4E,EAE5E;IAAEM,aAAa,EAAE;MAAEC,OAAO,EAAE;KAA5B;IAAoCC,OAAO,EAAE,IAAIC,GAAJ;GAF+B,CAA9E;EAKA,MAAMC,kBAAwE,GAAIC,KAAD,IAAW;IAC1F,MAAM;MArCVC,KAAA,EAqCYA,KAAF;MArCVC,QAAA,EAqCmBA;IAAA,CAAT,GAAsBF,KAA5B;IACA,MAAMG,GAAG,GAAGC,YAAK,CAACC,MAAN,CAAgC,IAAhC,CAAZ;IACA,MAAMR,OAAO,GAAGO,YAAK,CAACC,MAAN,CAAsC,IAAIP,GAAJ,EAAtC,EAAiDF,OAAjE;IACA,oBACEQ,YAAA,CAAAE,aAAA,CAACb,sBAAD,EADF;MAC0BQ,KAAK,EAAEA,KAA/B;MAAsCJ,OAAO,EAAEA,OAA/C;MAAwDF,aAAa,EAAEQ;KAAvE,EACGD,QADH,CADF;GAJF;EAWA;EAAAK,MAAA,CAAAC,MAAA,CAAAT,kBAAA;IAAAU,WAAA,EAAApB;GAAA;EAEA;;;EAIA,MAAMqB,oBAAoB,GAAGtB,IAAI,GAAG,gBAApC;EAEA,MAAMuB,cAAc,gBAAGP,YAAK,CAACQ,UAAN,CACrB,CAACZ,KAAD,EAAQa,YAAR,KAAyB;IACvB,MAAM;MAzDZZ,KAAA,EAyDcA,KAAF;MAzDZC,QAAA,EAyDqBA;IAAA,CAAT,GAAsBF,KAA5B;IACA,MAAMc,OAAO,GAAGpB,oBAAoB,CAACgB,oBAAD,EAAuBT,KAAvB,CAApC;IACA,MAAMc,YAAY,GAAGC,sBAAe,CAACH,YAAD,EAAeC,OAAO,CAACnB,aAAvB,CAApC;IACA,oBAAOS,YAAA,CAAAE,aAAA,CAACW,WAAD,EAAP;MAAad,GAAG,EAAEY;KAAX,EAA0Bb,QAA1B,CAAP;GALmB,CAAvB;EASA;EAAAK,MAAA,CAAAC,MAAA,CAAAG,cAAA;IAAAF,WAAA,EAAAC;GAAA;EAEA;;;EAIA,MAAMQ,cAAc,GAAG9B,IAAI,GAAG,oBAA9B;EACA,MAAM+B,cAAc,GAAG,4BAAvB;EAOA,MAAMC,kBAAkB,gBAAGhB,YAAK,CAACQ,UAAN,CACzB,CAACZ,KAAD,EAAQa,YAAR,KAAyB;IACvB,MAAM;MAhFZZ,KAAA,EAgFcA,KAAF;MAhFZC,QAAA,EAgFqBA,QAAT;MAAmB,GAAGmB;IAAH,CAAnB,GAAmCrB,KAAzC;IACA,MAAMG,GAAG,GAAGC,YAAK,CAACC,MAAN,CAA0B,IAA1B,CAAZ;IACA,MAAMU,YAAY,GAAGC,sBAAe,CAACH,YAAD,EAAeV,GAAf,CAApC;IACA,MAAMW,OAAO,GAAGpB,oBAAoB,CAACwB,cAAD,EAAiBjB,KAAjB,CAApC;IAEAG,YAAK,CAACkB,SAAN,CAAgB,MAAM;MACpBR,OAAO,CAACjB,OAAR,CAAgB0B,GAAhB,CAAoBpB,GAApB,EAAyB;QAtFjCA,GAAA,EAsFmCA,GAAF;QAAO,GAAIkB;OAApC,CAAyB;MACzB,OAAO,MAAM,KAAKP,OAAO,CAACjB,OAAR,CAAgB2B,MAAhB,CAAuBrB,GAAvB,CAAlB;KAFF,CAGC;IAED,oBACEC,YAAA,CAAAE,aAAA,CAACW,WAAD,EADF;MACc,CAACE,cAAD,GAAkB,EAA9B;MAAoChB,GAAG,EAAEY;KAAzC,EACGb,QADH,CADF;GAZuB,CAA3B;EAoBA;EAAAK,MAAA,CAAAC,MAAA,CAAAY,kBAAA;IAAAX,WAAA,EAAAS;GAAA;EAEA;;;EAIA,SAASO,aAATA,CAAuBxB,KAAvB,EAAmC;IACjC,MAAMa,OAAO,GAAGpB,oBAAoB,CAACN,IAAI,GAAG,oBAAR,EAA8Ba,KAA9B,CAApC;IAEA,MAAMyB,QAAQ,GAAGtB,YAAK,CAACuB,WAAN,CAAkB,MAAM;MACvC,MAAMC,cAAc,GAAGd,OAAO,CAACnB,aAAR,CAAsBC,OAA7C;MACA,IAAI,CAACgC,cAAL,EAAqB,OAAO,EAAP;MACrB,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAN,CAAWH,cAAc,CAACI,gBAAf,CAAiC,IAAGb,cAAe,GAAnD,CAAX,CAArB;MACA,MAAMc,KAAK,GAAGH,KAAK,CAACC,IAAN,CAAWjB,OAAO,CAACjB,OAAR,CAAgBqC,MAAhB,EAAX,CAAd;MACA,MAAMC,YAAY,GAAGF,KAAK,CAACG,IAAN,CACnB,CAACC,CAAD,EAAIC,CAAJ,KAAUT,YAAY,CAACU,OAAb,CAAqBF,CAAC,CAAClC,GAAF,CAAMP,OAA3B,IAAuCiC,YAAY,CAACU,OAAb,CAAqBD,CAAC,CAACnC,GAAF,CAAMP,OAA3B,CAD9B,CAArB;MAGA,OAAOuC,YAAP;KARe,EASd,CAACrB,OAAO,CAACnB,aAAT,EAAwBmB,OAAO,CAACjB,OAAhC,CATc,CAAjB;IAWA,OAAO6B,QAAP;;EAGF,OAAO,CACL;IAAEc,QAAQ,EAAEzC,kBAAZ;IAAgC0C,IAAI,EAAE9B,cAAtC;IAAsD+B,QAAQ,EAAEtB;GAD3D,EAELK,aAFK,EAGLlC,qBAHK,CAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}