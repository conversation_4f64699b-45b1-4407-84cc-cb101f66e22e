{"ast": null, "code": "import { useCallback as $bnPw9$useCallback, useState as $bnPw9$useState, useRef as $bnPw9$useRef, useEffect as $bnPw9$useEffect } from \"react\";\nimport { useCallbackRef as $bnPw9$useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction $71cd76cc60e0454e$export$6f32135080cb4c3({\n  prop: prop,\n  defaultProp: defaultProp,\n  onChange = () => {}\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = $71cd76cc60e0454e$var$useUncontrolledState({\n    defaultProp: defaultProp,\n    onChange: onChange\n  });\n  const isControlled = prop !== undefined;\n  const value1 = isControlled ? prop : uncontrolledProp;\n  const handleChange = $bnPw9$useCallbackRef(onChange);\n  const setValue = $bnPw9$useCallback(nextValue => {\n    if (isControlled) {\n      const setter = nextValue;\n      const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n      if (value !== prop) handleChange(value);\n    } else setUncontrolledProp(nextValue);\n  }, [isControlled, prop, setUncontrolledProp, handleChange]);\n  return [value1, setValue];\n}\nfunction $71cd76cc60e0454e$var$useUncontrolledState({\n  defaultProp: defaultProp,\n  onChange: onChange\n}) {\n  const uncontrolledState = $bnPw9$useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = $bnPw9$useRef(value);\n  const handleChange = $bnPw9$useCallbackRef(onChange);\n  $bnPw9$useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\nexport { $71cd76cc60e0454e$export$6f32135080cb4c3 as useControllableState };", "map": {"version": 3, "names": ["$71cd76cc60e0454e$export$6f32135080cb4c3", "useControllableState", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "$71cd76cc60e0454e$var$useUncontrolledState", "isControlled", "undefined", "value1", "handleChange", "$bnPw9$useCallbackRef", "setValue", "$bnPw9$useCallback", "nextValue", "setter", "value", "useUncontrolledState", "uncontrolledState", "$bnPw9$useState", "prevValueRef", "$bnPw9$useRef", "$bnPw9$useEffect", "current"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-controllable-state\\dist\\packages\\react\\use-controllable-state\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-controllable-state\\dist\\packages\\react\\use-controllable-state\\src\\useControllableState.tsx"], "sourcesContent": ["export { useControllableState } from './useControllableState';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n"], "mappings": ";;ACWA,SAASA,wCAATC,CAAiC;EAXjCC,IAAA,EAYEA,IAD+B;EAXjCC,WAAA,EAaEA,WAF+B;EAG/BC,QAAQ,GAAGA,CAAA,KAAM;AAAjB,CAHF,EAIkC;EAChC,MAAM,CAACC,gBAAD,EAAmBC,mBAAnB,IAA0CC,0CAAoB,CAAC;IAhBvEJ,WAAA,EAgByEA,WAAF;IAhBvEC,QAAA,EAgBsFA;GAAhB,CAApE;EACA,MAAMI,YAAY,GAAGN,IAAI,KAAKO,SAA9B;EACA,MAAMC,MAAK,GAAGF,YAAY,GAAGN,IAAH,GAAUG,gBAApC;EACA,MAAMM,YAAY,GAAGC,qBAAc,CAACR,QAAD,CAAnC;EAEA,MAAMS,QAA6D,GAAGC,kBAAA,CACnEC,SAAD,IAAe;IACb,IAAIP,YAAJ,EAAkB;MAChB,MAAMQ,MAAM,GAAGD,SAAf;MACA,MAAME,KAAK,GAAG,OAAOF,SAAP,KAAqB,UAArB,GAAkCC,MAAM,CAACd,IAAD,CAAxC,GAAiDa,SAA/D;MACA,IAAIE,KAAK,KAAKf,IAAd,EAAoBS,YAAY,CAACM,KAAD,CAAZ;KAHtB,MAKEX,mBAAmB,CAACS,SAAD,CAAnB;GAPgE,EAUpE,CAACP,YAAD,EAAeN,IAAf,EAAqBI,mBAArB,EAA0CK,YAA1C,CAVoE,CAAtE;EAaA,OAAO,CAACD,MAAD,EAAQG,QAAR,CAAP;;AAGF,SAASN,0CAATW,CAAiC;EArCjCf,WAAA,EAsCEA,WAD+B;EArCjCC,QAAA,EAuCEA;AAAA,CAFF,EAGgD;EAC9C,MAAMe,iBAAiB,GAAGC,eAAA,CAA8BjB,WAA9B,CAA1B;EACA,MAAM,CAACc,KAAD,IAAUE,iBAAhB;EACA,MAAME,YAAY,GAAGC,aAAA,CAAaL,KAAb,CAArB;EACA,MAAMN,YAAY,GAAGC,qBAAc,CAACR,QAAD,CAAnC;EAEAmB,gBAAA,CAAgB,MAAM;IACpB,IAAIF,YAAY,CAACG,OAAb,KAAyBP,KAA7B,EAAoC;MAClCN,YAAY,CAACM,KAAD,CAAZ;MACAI,YAAY,CAACG,OAAb,GAAuBP,KAAvB;;GAHJ,EAKG,CAACA,KAAD,EAAQI,YAAR,EAAsBV,YAAtB,CALH,CAKC;EAED,OAAOQ,iBAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}