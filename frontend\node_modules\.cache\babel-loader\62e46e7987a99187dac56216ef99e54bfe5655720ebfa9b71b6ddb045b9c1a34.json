{"ast": null, "code": "function r(e) {\n  var t,\n    f,\n    n = \"\";\n  if (\"string\" == typeof e || \"number\" == typeof e) n += e;else if (\"object\" == typeof e) if (Array.isArray(e)) {\n    var o = e.length;\n    for (t = 0; t < o; t++) e[t] && (f = r(e[t])) && (n && (n += \" \"), n += f);\n  } else for (f in e) e[f] && (n && (n += \" \"), n += f);\n  return n;\n}\nexport function clsx() {\n  for (var e, t, f = 0, n = \"\", o = arguments.length; f < o; f++) (e = arguments[f]) && (t = r(e)) && (n && (n += \" \"), n += t);\n  return n;\n}\nexport default clsx;", "map": {"version": 3, "names": ["r", "e", "t", "f", "n", "Array", "isArray", "o", "length", "clsx", "arguments"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;IAACC,CAAC;IAACC,CAAC,GAAC,EAAE;EAAC,IAAG,QAAQ,IAAE,OAAOH,CAAC,IAAE,QAAQ,IAAE,OAAOA,CAAC,EAACG,CAAC,IAAEH,CAAC,CAAC,KAAK,IAAG,QAAQ,IAAE,OAAOA,CAAC,EAAC,IAAGI,KAAK,CAACC,OAAO,CAACL,CAAC,CAAC,EAAC;IAAC,IAAIM,CAAC,GAACN,CAAC,CAACO,MAAM;IAAC,KAAIN,CAAC,GAAC,CAAC,EAACA,CAAC,GAACK,CAAC,EAACL,CAAC,EAAE,EAACD,CAAC,CAACC,CAAC,CAAC,KAAGC,CAAC,GAACH,CAAC,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,KAAGA,CAAC,IAAE,GAAG,CAAC,EAACA,CAAC,IAAED,CAAC,CAAC;EAAA,CAAC,MAAK,KAAIA,CAAC,IAAIF,CAAC,EAACA,CAAC,CAACE,CAAC,CAAC,KAAGC,CAAC,KAAGA,CAAC,IAAE,GAAG,CAAC,EAACA,CAAC,IAAED,CAAC,CAAC;EAAC,OAAOC,CAAC;AAAA;AAAC,OAAO,SAASK,IAAIA,CAAA,EAAE;EAAC,KAAI,IAAIR,CAAC,EAACC,CAAC,EAACC,CAAC,GAAC,CAAC,EAACC,CAAC,GAAC,EAAE,EAACG,CAAC,GAACG,SAAS,CAACF,MAAM,EAACL,CAAC,GAACI,CAAC,EAACJ,CAAC,EAAE,EAAC,CAACF,CAAC,GAACS,SAAS,CAACP,CAAC,CAAC,MAAID,CAAC,GAACF,CAAC,CAACC,CAAC,CAAC,CAAC,KAAGG,CAAC,KAAGA,CAAC,IAAE,GAAG,CAAC,EAACA,CAAC,IAAEF,CAAC,CAAC;EAAC,OAAOE,CAAC;AAAA;AAAC,eAAeK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}