{"ast": null, "code": "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);", "map": {"version": 3, "names": ["exportSidecar", "RemoveScrollSideCar", "effectCar"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;AAC3C,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,SAAS,QAAQ,UAAU;AACpC,eAAeF,aAAa,CAACE,SAAS,EAAED,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}