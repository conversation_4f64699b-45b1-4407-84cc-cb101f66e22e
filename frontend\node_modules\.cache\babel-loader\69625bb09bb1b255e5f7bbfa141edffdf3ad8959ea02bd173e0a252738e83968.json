{"ast": null, "code": "var dateLongFormatter = function dateLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'P':\n      return formatLong.date({\n        width: 'short'\n      });\n    case 'PP':\n      return formatLong.date({\n        width: 'medium'\n      });\n    case 'PPP':\n      return formatLong.date({\n        width: 'long'\n      });\n    case 'PPPP':\n    default:\n      return formatLong.date({\n        width: 'full'\n      });\n  }\n};\nvar timeLongFormatter = function timeLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'p':\n      return formatLong.time({\n        width: 'short'\n      });\n    case 'pp':\n      return formatLong.time({\n        width: 'medium'\n      });\n    case 'ppp':\n      return formatLong.time({\n        width: 'long'\n      });\n    case 'pppp':\n    default:\n      return formatLong.time({\n        width: 'full'\n      });\n  }\n};\nvar dateTimeLongFormatter = function dateTimeLongFormatter(pattern, formatLong) {\n  var matchResult = pattern.match(/(P+)(p+)?/) || [];\n  var datePattern = matchResult[1];\n  var timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  var dateTimeFormat;\n  switch (datePattern) {\n    case 'P':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'short'\n      });\n      break;\n    case 'PP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'medium'\n      });\n      break;\n    case 'PPP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'long'\n      });\n      break;\n    case 'PPPP':\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: 'full'\n      });\n      break;\n  }\n  return dateTimeFormat.replace('{{date}}', dateLongFormatter(datePattern, formatLong)).replace('{{time}}', timeLongFormatter(timePattern, formatLong));\n};\nvar longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};\nexport default longFormatters;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "formatLong", "date", "width", "time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time", "dateTimeLongFormatter", "matchResult", "match", "datePattern", "timePattern", "dateTimeFormat", "dateTime", "replace", "longFormatters", "p", "P"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/date-fns/esm/_lib/format/longFormatters/index.js"], "sourcesContent": ["var dateLongFormatter = function dateLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'P':\n      return formatLong.date({\n        width: 'short'\n      });\n    case 'PP':\n      return formatLong.date({\n        width: 'medium'\n      });\n    case 'PPP':\n      return formatLong.date({\n        width: 'long'\n      });\n    case 'PPPP':\n    default:\n      return formatLong.date({\n        width: 'full'\n      });\n  }\n};\nvar timeLongFormatter = function timeLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'p':\n      return formatLong.time({\n        width: 'short'\n      });\n    case 'pp':\n      return formatLong.time({\n        width: 'medium'\n      });\n    case 'ppp':\n      return formatLong.time({\n        width: 'long'\n      });\n    case 'pppp':\n    default:\n      return formatLong.time({\n        width: 'full'\n      });\n  }\n};\nvar dateTimeLongFormatter = function dateTimeLongFormatter(pattern, formatLong) {\n  var matchResult = pattern.match(/(P+)(p+)?/) || [];\n  var datePattern = matchResult[1];\n  var timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  var dateTimeFormat;\n  switch (datePattern) {\n    case 'P':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'short'\n      });\n      break;\n    case 'PP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'medium'\n      });\n      break;\n    case 'PPP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'long'\n      });\n      break;\n    case 'PPPP':\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: 'full'\n      });\n      break;\n  }\n  return dateTimeFormat.replace('{{date}}', dateLongFormatter(datePattern, formatLong)).replace('{{time}}', timeLongFormatter(timePattern, formatLong));\n};\nvar longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};\nexport default longFormatters;"], "mappings": "AAAA,IAAIA,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACtE,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,IAAI;MACP,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,KAAK;MACR,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;EACN;AACF,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACJ,OAAO,EAAEC,UAAU,EAAE;EACtE,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,IAAI;MACP,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,KAAK;MACR,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;EACN;AACF,CAAC;AACD,IAAIG,qBAAqB,GAAG,SAASA,qBAAqBA,CAACN,OAAO,EAAEC,UAAU,EAAE;EAC9E,IAAIM,WAAW,GAAGP,OAAO,CAACQ,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;EAClD,IAAIC,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAChC,IAAIG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;EAChC,IAAI,CAACG,WAAW,EAAE;IAChB,OAAOX,iBAAiB,CAACC,OAAO,EAAEC,UAAU,CAAC;EAC/C;EACA,IAAIU,cAAc;EAClB,QAAQF,WAAW;IACjB,KAAK,GAAG;MACNE,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IACF,KAAK,IAAI;MACPQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IACF,KAAK,KAAK;MACRQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IACF,KAAK,MAAM;IACX;MACEQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;EACJ;EACA,OAAOQ,cAAc,CAACE,OAAO,CAAC,UAAU,EAAEd,iBAAiB,CAACU,WAAW,EAAER,UAAU,CAAC,CAAC,CAACY,OAAO,CAAC,UAAU,EAAET,iBAAiB,CAACM,WAAW,EAAET,UAAU,CAAC,CAAC;AACvJ,CAAC;AACD,IAAIa,cAAc,GAAG;EACnBC,CAAC,EAAEX,iBAAiB;EACpBY,CAAC,EAAEV;AACL,CAAC;AACD,eAAeQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}