{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigitsSigned } from \"../utils.js\";\nexport var ExtendedYearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(ExtendedYearParser, _Parser);\n  var _super = _createSuper(ExtendedYearParser);\n  function ExtendedYearParser() {\n    var _this;\n    _classCallCheck(this, ExtendedYearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['G', 'y', 'Y', 'R', 'w', 'I', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(ExtendedYearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token) {\n      if (token === 'u') {\n        return parseNDigitsSigned(4, dateString);\n      }\n      return parseNDigitsSigned(token.length, dateString);\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCFullYear(value, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return ExtendedYearParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "parseNDigitsSigned", "<PERSON><PERSON>ear<PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "token", "set", "date", "_flags", "setUTCFullYear", "setUTCHours"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/date-fns/esm/parse/_lib/parsers/ExtendedYearParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigitsSigned } from \"../utils.js\";\nexport var ExtendedYearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(ExtendedYearParser, _Parser);\n  var _super = _createSuper(ExtendedYearParser);\n  function ExtendedYearParser() {\n    var _this;\n    _classCallCheck(this, ExtendedYearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['G', 'y', 'Y', 'R', 'w', 'I', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(ExtendedYearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token) {\n      if (token === 'u') {\n        return parseNDigitsSigned(4, dateString);\n      }\n      return parseNDigitsSigned(token.length, dateString);\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCFullYear(value, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return ExtendedYearParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,kBAAkB,QAAQ,aAAa;AAChD,OAAO,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,OAAO,EAAE;EAC9DN,SAAS,CAACK,kBAAkB,EAAEC,OAAO,CAAC;EACtC,IAAIC,MAAM,GAAGN,YAAY,CAACI,kBAAkB,CAAC;EAC7C,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAIG,KAAK;IACTX,eAAe,CAAC,IAAI,EAAEQ,kBAAkB,CAAC;IACzC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDV,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC;IAC/DN,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7H,OAAOA,KAAK;EACd;EACAV,YAAY,CAACO,kBAAkB,EAAE,CAAC;IAChCa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;MACvC,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjB,OAAOlB,kBAAkB,CAAC,CAAC,EAAEiB,UAAU,CAAC;MAC1C;MACA,OAAOjB,kBAAkB,CAACkB,KAAK,CAACX,MAAM,EAAEU,UAAU,CAAC;IACrD;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASI,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEN,KAAK,EAAE;MACvCK,IAAI,CAACE,cAAc,CAACP,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAChCK,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5B,OAAOH,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOnB,kBAAkB;AAC3B,CAAC,CAACF,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}