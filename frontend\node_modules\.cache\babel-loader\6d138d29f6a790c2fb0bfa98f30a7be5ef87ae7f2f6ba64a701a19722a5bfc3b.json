{"ast": null, "code": "import { twJoin } from './lib/tw-join.mjs';\nexport { createTailwindMerge } from './lib/create-tailwind-merge.mjs';\nexport { getDefaultConfig } from './lib/default-config.mjs';\nexport { extendTailwindMerge } from './lib/extend-tailwind-merge.mjs';\nexport { fromTheme } from './lib/from-theme.mjs';\nexport { mergeConfigs } from './lib/merge-configs.mjs';\nexport { twMerge } from './lib/tw-merge.mjs';\nimport * as validators from './lib/validators.mjs';\nexport { validators };\n\n/**\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\n */\nvar join = twJoin;\nexport { join, twJoin };", "map": {"version": 3, "names": ["join", "twJoin"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\index.ts"], "sourcesContent": ["import { twJoin } from './lib/tw-join'\n\nexport { createTailwindMerge } from './lib/create-tailwind-merge'\nexport { getDefaultConfig } from './lib/default-config'\nexport { extendTailwindMerge } from './lib/extend-tailwind-merge'\nexport { fromTheme } from './lib/from-theme'\nexport { mergeConfigs } from './lib/merge-configs'\nexport { twJoin, type ClassNameValue } from './lib/tw-join'\nexport { twMerge } from './lib/tw-merge'\nexport type { Config } from './lib/types'\nexport * as validators from './lib/validators'\n\n/**\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\n */\nexport const join = twJoin\n"], "mappings": ";;;;;;;;;;AAYA;;AAEG;AACI,IAAMA,IAAI,GAAGC,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}