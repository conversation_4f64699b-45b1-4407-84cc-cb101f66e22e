{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst SunSnow = createLucideIcon(\"SunSnow\", [[\"path\", {\n  d: \"M10 9a3 3 0 1 0 0 6\",\n  key: \"6zmtdl\"\n}], [\"path\", {\n  d: \"M2 12h1\",\n  key: \"1uaihz\"\n}], [\"path\", {\n  d: \"M14 21V3\",\n  key: \"1llu3z\"\n}], [\"path\", {\n  d: \"M10 4V3\",\n  key: \"pkzwkn\"\n}], [\"path\", {\n  d: \"M10 21v-1\",\n  key: \"1u8rkd\"\n}], [\"path\", {\n  d: \"m3.64 18.36.7-.7\",\n  key: \"105rm9\"\n}], [\"path\", {\n  d: \"m4.34 6.34-.7-.7\",\n  key: \"d3unjp\"\n}], [\"path\", {\n  d: \"M14 12h8\",\n  key: \"4f43i9\"\n}], [\"path\", {\n  d: \"m17 4-3 3\",\n  key: \"15jcng\"\n}], [\"path\", {\n  d: \"m14 17 3 3\",\n  key: \"6tlq38\"\n}], [\"path\", {\n  d: \"m21 15-3-3 3-3\",\n  key: \"1nlnje\"\n}]]);\nexport { SunSnow as default };", "map": {"version": 3, "names": ["SunSnow", "createLucideIcon", "d", "key"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\sun-snow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SunSnow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgOWEzIDMgMCAxIDAgMCA2IiAvPgogIDxwYXRoIGQ9Ik0yIDEyaDEiIC8+CiAgPHBhdGggZD0iTTE0IDIxVjMiIC8+CiAgPHBhdGggZD0iTTEwIDRWMyIgLz4KICA8cGF0aCBkPSJNMTAgMjF2LTEiIC8+CiAgPHBhdGggZD0ibTMuNjQgMTguMzYuNy0uNyIgLz4KICA8cGF0aCBkPSJtNC4zNCA2LjM0LS43LS43IiAvPgogIDxwYXRoIGQ9Ik0xNCAxMmg4IiAvPgogIDxwYXRoIGQ9Im0xNyA0LTMgMyIgLz4KICA8cGF0aCBkPSJtMTQgMTcgMyAzIiAvPgogIDxwYXRoIGQ9Im0yMSAxNS0zLTMgMy0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sun-snow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SunSnow = createLucideIcon('SunSnow', [\n  ['path', { d: 'M10 9a3 3 0 1 0 0 6', key: '6zmtdl' }],\n  ['path', { d: 'M2 12h1', key: '1uaihz' }],\n  ['path', { d: 'M14 21V3', key: '1llu3z' }],\n  ['path', { d: 'M10 4V3', key: 'pkzwkn' }],\n  ['path', { d: 'M10 21v-1', key: '1u8rkd' }],\n  ['path', { d: 'm3.64 18.36.7-.7', key: '105rm9' }],\n  ['path', { d: 'm4.34 6.34-.7-.7', key: 'd3unjp' }],\n  ['path', { d: 'M14 12h8', key: '4f43i9' }],\n  ['path', { d: 'm17 4-3 3', key: '15jcng' }],\n  ['path', { d: 'm14 17 3 3', key: '6tlq38' }],\n  ['path', { d: 'm21 15-3-3 3-3', key: '1nlnje' }],\n]);\n\nexport default SunSnow;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}