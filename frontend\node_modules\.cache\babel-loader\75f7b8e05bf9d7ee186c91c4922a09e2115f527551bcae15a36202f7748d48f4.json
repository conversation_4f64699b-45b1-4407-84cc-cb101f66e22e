{"ast": null, "code": "// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nfunction createLruCache(maxCacheSize) {\n  if (maxCacheSize < 1) {\n    return {\n      get: function get() {\n        return undefined;\n      },\n      set: function set() {}\n    };\n  }\n  var cacheSize = 0;\n  var cache = new Map();\n  var previousCache = new Map();\n  function update(key, value) {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  }\n  return {\n    get: function get(key) {\n      var value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set: function set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n}\nexport { createLruCache };", "map": {"version": 3, "names": ["createLruCache", "maxCacheSize", "get", "undefined", "set", "cacheSize", "cache", "Map", "previousCache", "update", "key", "value", "has"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\lru-cache.ts"], "sourcesContent": ["// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport function createLruCache<Key, Value>(maxCacheSize: number): LruCache<Key, Value> {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    function update(key: Key, value: Value) {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n"], "mappings": "AAOA;AACM,SAAUA,cAAcA,CAAaC,YAAoB;EAC3D,IAAIA,YAAY,GAAG,CAAC,EAAE;IAClB,OAAO;MACHC,GAAG,EAAE,SAAAA,IAAA;QAAA,OAAMC,SAAS;MAAA;MACpBC,GAAG,EAAE,SAAAA,IAAA,EAAK;KACb;EACJ;EAED,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,KAAK,GAAG,IAAIC,GAAG,EAAc;EACjC,IAAIC,aAAa,GAAG,IAAID,GAAG,EAAc;EAEzC,SAASE,MAAMA,CAACC,GAAQ,EAAEC,KAAY;IAClCL,KAAK,CAACF,GAAG,CAACM,GAAG,EAAEC,KAAK,CAAC;IACrBN,SAAS,EAAE;IAEX,IAAIA,SAAS,GAAGJ,YAAY,EAAE;MAC1BI,SAAS,GAAG,CAAC;MACbG,aAAa,GAAGF,KAAK;MACrBA,KAAK,GAAG,IAAIC,GAAG,EAAE;IACpB;EACL;EAEA,OAAO;IACHL,GAAG,WAAAA,IAACQ,GAAG;MACH,IAAIC,KAAK,GAAGL,KAAK,CAACJ,GAAG,CAACQ,GAAG,CAAC;MAE1B,IAAIC,KAAK,KAAKR,SAAS,EAAE;QACrB,OAAOQ,KAAK;MACf;MACD,IAAI,CAACA,KAAK,GAAGH,aAAa,CAACN,GAAG,CAACQ,GAAG,CAAC,MAAMP,SAAS,EAAE;QAChDM,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC;QAClB,OAAOA,KAAK;MACf;KACJ;IACDP,GAAG,EAAC,SAAAA,IAAAM,GAAG,EAAEC,KAAK;MACV,IAAIL,KAAK,CAACM,GAAG,CAACF,GAAG,CAAC,EAAE;QAChBJ,KAAK,CAACF,GAAG,CAACM,GAAG,EAAEC,KAAK,CAAC;MACxB,OAAM;QACHF,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC;MACrB;IACL;GACH;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}