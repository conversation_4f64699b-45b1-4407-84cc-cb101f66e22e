{"ast": null, "code": "import { useCallback as $3vqmr$useCallback } from \"react\";\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction $6ed0406888f73fc4$var$setRef(ref, value) {\n  if (typeof ref === 'function') ref(value);else if (ref !== null && ref !== undefined) ref.current = value;\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction $6ed0406888f73fc4$export$43e446d32b3d21af(...refs) {\n  return node => refs.forEach(ref => $6ed0406888f73fc4$var$setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction $6ed0406888f73fc4$export$c7b2cbe3552a0d05(...refs) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return $3vqmr$useCallback($6ed0406888f73fc4$export$43e446d32b3d21af(...refs), refs);\n}\nexport { $6ed0406888f73fc4$export$43e446d32b3d21af as composeRefs, $6ed0406888f73fc4$export$c7b2cbe3552a0d05 as useComposedRefs };", "map": {"version": 3, "names": ["$6ed0406888f73fc4$var$setRef", "setRef", "ref", "value", "undefined", "current", "$6ed0406888f73fc4$export$43e446d32b3d21af", "composeRefs", "refs", "node", "for<PERSON>ach", "$6ed0406888f73fc4$export$c7b2cbe3552a0d05", "useComposedRefs", "$3vqmr$useCallback"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-compose-refs\\dist\\packages\\react\\compose-refs\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-compose-refs\\dist\\packages\\react\\compose-refs\\src\\composeRefs.tsx"], "sourcesContent": ["export { composeRefs, useComposedRefs } from './composeRefs';\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "mappings": ";;ACIA;;;;AAIA,SAASA,4BAATC,CAAmBC,GAAnB,EAAwCC,KAAxC,EAAkD;EAChD,IAAI,OAAOD,GAAP,KAAe,UAAnB,EACEA,GAAG,CAACC,KAAD,CAAH,MACK,IAAID,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKE,SAA5B,EACJF,GAAD,CAAmCG,OAAnC,GAA6CF,KAA7C;;AAIJ;;;;AAIA,SAASG,yCAATC,CAAwB,GAAGC,IAA3B,EAAmD;EACjD,OAAQC,IAAD,IAAaD,IAAI,CAACE,OAAL,CAAcR,GAAD,IAASF,4BAAM,CAACE,GAAD,EAAMO,IAAN,CAA5B,CAApB;;AAGF;;;;AAIA,SAASE,yCAATC,CAA4B,GAAGJ,IAA/B,EAAuD;EACrD;EACA,OAAOK,kBAAA,CAAkBP,yCAAW,IAAIE,IAAJ,CAA7B,EAAwCA,IAAxC,CAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}