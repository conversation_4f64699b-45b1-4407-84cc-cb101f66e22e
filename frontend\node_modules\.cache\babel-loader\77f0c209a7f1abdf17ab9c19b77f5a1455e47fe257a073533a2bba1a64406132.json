{"ast": null, "code": "function $ae6933e535247d3d$export$7d15b64cf5a3a4c4(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport { $ae6933e535247d3d$export$7d15b64cf5a3a4c4 as clamp };", "map": {"version": 3, "names": ["$ae6933e535247d3d$export$7d15b64cf5a3a4c4", "clamp", "value", "min", "max", "Math"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\number\\dist\\packages\\core\\number\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\number\\dist\\packages\\core\\number\\src\\number.ts"], "sourcesContent": ["export { clamp } from './number';\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "mappings": "ACAA,SAASA,yCAATC,CAAeC,KAAf,EAA8B,CAACC,GAAD,EAAMC,GAAN,CAA9B,EAAoE;EAClE,OAAOC,IAAI,CAACF,GAAL,CAASC,GAAT,EAAcC,IAAI,CAACD,GAAL,CAASD,GAAT,EAAcD,KAAd,CAAd,CAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}