{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst UserMinus = createLucideIcon(\"UserMinus\", [[\"path\", {\n  d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n  key: \"1yyitq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"nufk8\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"16\",\n  y1: \"11\",\n  y2: \"11\",\n  key: \"1shjgl\"\n}]]);\nexport { UserMinus as default };", "map": {"version": 3, "names": ["UserMinus", "createLucideIcon", "d", "key", "cx", "cy", "r", "x1", "x2", "y1", "y2"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\user-minus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserMinus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIxNiIgeTE9IjExIiB5Mj0iMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-minus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserMinus = createLucideIcon('UserMinus', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['line', { x1: '22', x2: '16', y1: '11', y2: '11', key: '1shjgl' }],\n]);\n\nexport default UserMinus;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAS,GACrD,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAP,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}