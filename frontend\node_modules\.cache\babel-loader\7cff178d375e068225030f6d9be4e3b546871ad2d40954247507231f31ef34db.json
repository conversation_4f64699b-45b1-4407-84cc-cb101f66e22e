{"ast": null, "code": "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n  var ErrorCase = function () {\n    return errorComponent;\n  };\n  return function Sidecar(props) {\n    var _a = useSidecar(importer, props.sideCar),\n      Car = _a[0],\n      error = _a[1];\n    if (error && errorComponent) {\n      return ErrorCase;\n    }\n    // @ts-expect-error type shenanigans\n    return Car ? React.createElement(Car, __assign({}, props)) : null;\n  };\n}", "map": {"version": 3, "names": ["__assign", "React", "useSidecar", "sidecar", "importer", "errorComponent", "ErrorCase", "Sidecar", "props", "_a", "sideCar", "Car", "error", "createElement"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-sidecar/dist/es2015/hoc.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { useSidecar } from './hook';\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function sidecar(importer, errorComponent) {\n    var ErrorCase = function () { return errorComponent; };\n    return function Sidecar(props) {\n        var _a = useSidecar(importer, props.sideCar), Car = _a[0], error = _a[1];\n        if (error && errorComponent) {\n            return ErrorCase;\n        }\n        // @ts-expect-error type shenanigans\n        return Car ? React.createElement(Car, __assign({}, props)) : null;\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,QAAQ;AACnC;AACA,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAEC,cAAc,EAAE;EAC9C,IAAIC,SAAS,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOD,cAAc;EAAE,CAAC;EACtD,OAAO,SAASE,OAAOA,CAACC,KAAK,EAAE;IAC3B,IAAIC,EAAE,GAAGP,UAAU,CAACE,QAAQ,EAAEI,KAAK,CAACE,OAAO,CAAC;MAAEC,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;MAAEG,KAAK,GAAGH,EAAE,CAAC,CAAC,CAAC;IACxE,IAAIG,KAAK,IAAIP,cAAc,EAAE;MACzB,OAAOC,SAAS;IACpB;IACA;IACA,OAAOK,GAAG,GAAGV,KAAK,CAACY,aAAa,CAACF,GAAG,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAAC,CAAC,GAAG,IAAI;EACrE,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}