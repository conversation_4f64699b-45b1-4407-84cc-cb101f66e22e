{"ast": null, "code": "import $7SXl2$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $7SXl2$forwardRef, createElement as $7SXl2$createElement } from \"react\";\nimport $7SXl2$reactdom from \"react-dom\";\nimport { Primitive as $7SXl2$Primitive } from \"@radix-ui/react-primitive\";\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\nconst $f1701beae083dbae$var$PORTAL_NAME = 'Portal';\nconst $f1701beae083dbae$export$602eac185826482c = /*#__PURE__*/$7SXl2$forwardRef((props, forwardedRef) => {\n  var _globalThis$document;\n  const {\n    container = globalThis === null || globalThis === void 0 ? void 0 : (_globalThis$document = globalThis.document) === null || _globalThis$document === void 0 ? void 0 : _globalThis$document.body,\n    ...portalProps\n  } = props;\n  return container ? /*#__PURE__*/$7SXl2$reactdom.createPortal(/*#__PURE__*/$7SXl2$createElement($7SXl2$Primitive.div, $7SXl2$babelruntimehelpersesmextends({}, portalProps, {\n    ref: forwardedRef\n  })), container) : null;\n});\n/*#__PURE__*/\nObject.assign($f1701beae083dbae$export$602eac185826482c, {\n  displayName: $f1701beae083dbae$var$PORTAL_NAME\n});\n/* -----------------------------------------------------------------------------------------------*/\nconst $f1701beae083dbae$export$be92b6f5f03c0fe9 = $f1701beae083dbae$export$602eac185826482c;\nexport { $f1701beae083dbae$export$602eac185826482c as Portal, $f1701beae083dbae$export$be92b6f5f03c0fe9 as Root };", "map": {"version": 3, "names": ["$f1701beae083dbae$var$PORTAL_NAME", "$f1701beae083dbae$export$602eac185826482c", "$7SXl2$forwardRef", "props", "forwardedRef", "_globalThis$document", "container", "globalThis", "document", "body", "portalProps", "$7SXl2$reactdom", "createPortal", "$7SXl2$createElement", "$7SXl2$Primitive", "div", "$7SXl2$babelruntimehelpersesmextends", "ref", "Object", "assign", "displayName", "$f1701beae083dbae$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-portal\\dist\\packages\\react\\portal\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-portal\\dist\\packages\\react\\portal\\src\\Portal.tsx"], "sourcesContent": ["export {\n  Portal,\n  //\n  Root,\n} from './Portal';\nexport type { PortalProps } from './Portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  container?: HTMLElement | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container = globalThis?.document?.body, ...portalProps } = props;\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n"], "mappings": ";;;;;ACMA;;;AAIA,MAAMA,iCAAW,GAAG,QAApB;AAQA,MAAMC,yCAAM,gBAAGC,iBAAA,CAA6C,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAAA,IAAAC,oBAAA;EACnF,MAAM;IAAEC,SAAS,GAAGC,UAAH,aAAGA,UAAH,wBAAAF,oBAAA,GAAGE,UAAU,CAAEC,QAAf,cAAAH,oBAAA,uBAAGA,oBAAA,CAAsBI,IAApC;IAA0C,GAAGC;EAAH,CAA1C,GAA6DP,KAAnE;EACA,OAAOG,SAAS,gBACZK,eAAQ,CAACC,YAAT,cAAsBC,oBAAA,CAACC,gBAAD,CAAWC,GAAX,EAAAC,oCAAA,KAAmBN,WAAnB,EAD1B;IAC0DO,GAAG,EAAEb;GAArC,EAAtB,EAA6EE,SAA7E,CADY,GAEZ,IAFJ;CAFa,CAAf;AAOA;AAAAY,MAAA,CAAAC,MAAA,CAAAlB,yCAAA;EAAAmB,WAAA,EAAApB;CAAA;AAEA;AAEA,MAAMqB,yCAAI,GAAGpB,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}