{"ast": null, "code": "import { sortModifiers, IMPORTANT_MODIFIER } from './modifier-utils.mjs';\nvar SPLIT_CLASSES_REGEX = /\\s+/;\nfunction mergeClassList(classList, configUtils) {\n  var splitModifiers = configUtils.splitModifiers,\n    getClassGroupId = configUtils.getClassGroupId,\n    getConflictingClassGroupIds = configUtils.getConflictingClassGroupIds;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  var classGroupsInConflict = new Set();\n  return classList.trim().split(SPLIT_CLASSES_REGEX).map(function (originalClassName) {\n    var _splitModifiers = splitModifiers(originalClassName),\n      modifiers = _splitModifiers.modifiers,\n      hasImportantModifier = _splitModifiers.hasImportantModifier,\n      baseClassName = _splitModifiers.baseClassName,\n      maybePostfixModifierPosition = _splitModifiers.maybePostfixModifierPosition;\n    var classGroupId = getClassGroupId(maybePostfixModifierPosition ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    var hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    if (!classGroupId) {\n      if (!maybePostfixModifierPosition) {\n        return {\n          isTailwindClass: false,\n          originalClassName: originalClassName\n        };\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        return {\n          isTailwindClass: false,\n          originalClassName: originalClassName\n        };\n      }\n      hasPostfixModifier = false;\n    }\n    var variantModifier = sortModifiers(modifiers).join(':');\n    var modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    return {\n      isTailwindClass: true,\n      modifierId: modifierId,\n      classGroupId: classGroupId,\n      originalClassName: originalClassName,\n      hasPostfixModifier: hasPostfixModifier\n    };\n  }).reverse()\n  // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n  .filter(function (parsed) {\n    if (!parsed.isTailwindClass) {\n      return true;\n    }\n    var modifierId = parsed.modifierId,\n      classGroupId = parsed.classGroupId,\n      hasPostfixModifier = parsed.hasPostfixModifier;\n    var classId = modifierId + classGroupId;\n    if (classGroupsInConflict.has(classId)) {\n      return false;\n    }\n    classGroupsInConflict.add(classId);\n    getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach(function (group) {\n      return classGroupsInConflict.add(modifierId + group);\n    });\n    return true;\n  }).reverse().map(function (parsed) {\n    return parsed.originalClassName;\n  }).join(' ');\n}\nexport { mergeClassList };", "map": {"version": 3, "names": ["SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "splitModifiers", "getClassGroupId", "getConflictingClassGroupIds", "classGroupsInConflict", "Set", "trim", "split", "map", "originalClassName", "_splitModifiers", "modifiers", "hasImportantModifier", "baseClassName", "maybePostfixModifierPosition", "classGroupId", "substring", "hasPostfixModifier", "Boolean", "isTailwindClass", "variantModifier", "sortModifiers", "join", "modifierId", "IMPORTANT_MODIFIER", "reverse", "filter", "parsed", "classId", "has", "add", "for<PERSON>ach", "group"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\merge-classlist.ts"], "sourcesContent": ["import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './modifier-utils'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport function mergeClassList(classList: string, configUtils: ConfigUtils) {\n    const { splitModifiers, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict = new Set<string>()\n\n    return (\n        classList\n            .trim()\n            .split(SPLIT_CLASSES_REGEX)\n            .map((originalClassName) => {\n                const {\n                    modifiers,\n                    hasImportantModifier,\n                    baseClassName,\n                    maybePostfixModifierPosition,\n                } = splitModifiers(originalClassName)\n\n                let classGroupId = getClassGroupId(\n                    maybePostfixModifierPosition\n                        ? baseClassName.substring(0, maybePostfixModifierPosition)\n                        : baseClassName,\n                )\n\n                let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n\n                if (!classGroupId) {\n                    if (!maybePostfixModifierPosition) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    classGroupId = getClassGroupId(baseClassName)\n\n                    if (!classGroupId) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    hasPostfixModifier = false\n                }\n\n                const variantModifier = sortModifiers(modifiers).join(':')\n\n                const modifierId = hasImportantModifier\n                    ? variantModifier + IMPORTANT_MODIFIER\n                    : variantModifier\n\n                return {\n                    isTailwindClass: true as const,\n                    modifierId,\n                    classGroupId,\n                    originalClassName,\n                    hasPostfixModifier,\n                }\n            })\n            .reverse()\n            // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n            .filter((parsed) => {\n                if (!parsed.isTailwindClass) {\n                    return true\n                }\n\n                const { modifierId, classGroupId, hasPostfixModifier } = parsed\n\n                const classId = modifierId + classGroupId\n\n                if (classGroupsInConflict.has(classId)) {\n                    return false\n                }\n\n                classGroupsInConflict.add(classId)\n\n                getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach((group) =>\n                    classGroupsInConflict.add(modifierId + group),\n                )\n\n                return true\n            })\n            .reverse()\n            .map((parsed) => parsed.originalClassName)\n            .join(' ')\n    )\n}\n"], "mappings": ";AAGA,IAAMA,mBAAmB,GAAG,KAAK;AAEjB,SAAAC,cAAcA,CAACC,SAAiB,EAAEC,WAAwB;EACtE,IAAQC,cAAc,GAAmDD,WAAW,CAA5EC,cAAc;IAAEC,eAAe,GAAkCF,WAAW,CAA5DE,eAAe;IAAEC,2BAA2B,GAAKH,WAAW,CAA3CG,2BAA2B;EAEpE;;;;;;AAMG;EACH,IAAMC,qBAAqB,GAAG,IAAIC,GAAG,EAAU;EAE/C,OACIN,SAAS,CACJO,IAAI,EAAE,CACNC,KAAK,CAACV,mBAAmB,CAAC,CAC1BW,GAAG,CAAC,UAACC,iBAAiB,EAAI;IACvB,IAKIC,eAAA,GAAAT,cAAc,CAACQ,iBAAiB,CAAC;MAJjCE,SAAS,GAAAD,eAAA,CAATC,SAAS;MACTC,oBAAoB,GAAAF,eAAA,CAApBE,oBAAoB;MACpBC,aAAa,GAAAH,eAAA,CAAbG,aAAa;MACbC,4BAA4B,GAAAJ,eAAA,CAA5BI,4BAA4B;IAGhC,IAAIC,YAAY,GAAGb,eAAe,CAC9BY,4BAA4B,GACtBD,aAAa,CAACG,SAAS,CAAC,CAAC,EAAEF,4BAA4B,CAAC,GACxDD,aAAa,CACtB;IAED,IAAII,kBAAkB,GAAGC,OAAO,CAACJ,4BAA4B,CAAC;IAE9D,IAAI,CAACC,YAAY,EAAE;MACf,IAAI,CAACD,4BAA4B,EAAE;QAC/B,OAAO;UACHK,eAAe,EAAE,KAAc;UAC/BV,iBAAiB,EAAjBA;SACH;MACJ;MAEDM,YAAY,GAAGb,eAAe,CAACW,aAAa,CAAC;MAE7C,IAAI,CAACE,YAAY,EAAE;QACf,OAAO;UACHI,eAAe,EAAE,KAAc;UAC/BV,iBAAiB,EAAjBA;SACH;MACJ;MAEDQ,kBAAkB,GAAG,KAAK;IAC7B;IAED,IAAMG,eAAe,GAAGC,aAAa,CAACV,SAAS,CAAC,CAACW,IAAI,CAAC,GAAG,CAAC;IAE1D,IAAMC,UAAU,GAAGX,oBAAoB,GACjCQ,eAAe,GAAGI,kBAAkB,GACpCJ,eAAe;IAErB,OAAO;MACHD,eAAe,EAAE,IAAa;MAC9BI,UAAU,EAAVA,UAAU;MACVR,YAAY,EAAZA,YAAY;MACZN,iBAAiB,EAAjBA,iBAAiB;MACjBQ,kBAAkB,EAAlBA;KACH;GACJ,CAAC,CACDQ,OAAO;EACR;EAAA,CACCC,MAAM,CAAC,UAACC,MAAM,EAAI;IACf,IAAI,CAACA,MAAM,CAACR,eAAe,EAAE;MACzB,OAAO,IAAI;IACd;IAED,IAAQI,UAAU,GAAuCI,MAAM,CAAvDJ,UAAU;MAAER,YAAY,GAAyBY,MAAM,CAA3CZ,YAAY;MAAEE,kBAAkB,GAAKU,MAAM,CAA7BV,kBAAkB;IAEpD,IAAMW,OAAO,GAAGL,UAAU,GAAGR,YAAY;IAEzC,IAAIX,qBAAqB,CAACyB,GAAG,CAACD,OAAO,CAAC,EAAE;MACpC,OAAO,KAAK;IACf;IAEDxB,qBAAqB,CAAC0B,GAAG,CAACF,OAAO,CAAC;IAElCzB,2BAA2B,CAACY,YAAY,EAAEE,kBAAkB,CAAC,CAACc,OAAO,CAAC,UAACC,KAAK;MAAA,OACxE5B,qBAAqB,CAAC0B,GAAG,CAACP,UAAU,GAAGS,KAAK,CAAC;KAChD;IAED,OAAO,IAAI;GACd,CAAC,CACDP,OAAO,EAAE,CACTjB,GAAG,CAAC,UAACmB,MAAM;IAAA,OAAKA,MAAM,CAAClB,iBAAiB;EAAA,EAAC,CACzCa,IAAI,CAAC,GAAG,CAAC;AAEtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}