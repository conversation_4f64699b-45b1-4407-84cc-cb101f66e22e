{"ast": null, "code": "import { createClassUtils } from './class-utils.mjs';\nimport { createLruCache } from './lru-cache.mjs';\nimport { createSplitModifiers } from './modifier-utils.mjs';\nfunction createConfigUtils(config) {\n  return {\n    cache: createLruCache(config.cacheSize),\n    splitModifiers: createSplitModifiers(config),\n    ...createClassUtils(config)\n  };\n}\nexport { createConfigUtils };", "map": {"version": 3, "names": ["createConfigUtils", "config", "cache", "createLruCache", "cacheSize", "splitModifiers", "createSplitModifiers", "createClassUtils"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\config-utils.ts"], "sourcesContent": ["import { createClassUtils } from './class-utils'\nimport { createLruCache } from './lru-cache'\nimport { createSplitModifiers } from './modifier-utils'\nimport { Config } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createConfigUtils(config: Config) {\n    return {\n        cache: createLruCache<string, string>(config.cacheSize),\n        splitModifiers: createSplitModifiers(config),\n        ...createClassUtils(config),\n    }\n}\n"], "mappings": ";;;AAOM,SAAUA,iBAAiBA,CAACC,MAAc;EAC5C,OAAO;IACHC,KAAK,EAAEC,cAAc,CAAiBF,MAAM,CAACG,SAAS,CAAC;IACvDC,cAAc,EAAEC,oBAAoB,CAACL,MAAM,CAAC;IAC5C,GAAGM,gBAAgB,CAACN,MAAM;GAC7B;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}