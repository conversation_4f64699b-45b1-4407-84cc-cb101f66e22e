{"ast": null, "code": "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };", "map": {"version": 3, "names": ["hasW<PERSON>ow", "window", "getNodeName", "node", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "ownerDocument", "defaultView", "getDocumentElement", "_ref", "document", "documentElement", "value", "Node", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "invalidOverflowDisplayValues", "Set", "isOverflowElement", "element", "overflow", "overflowX", "overflowY", "display", "getComputedStyle", "test", "has", "tableElements", "isTableElement", "topLayerSelectors", "isTop<PERSON><PERSON>er", "some", "selector", "matches", "_e", "transformProperties", "will<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containValues", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "filter", "<PERSON><PERSON><PERSON><PERSON>", "includes", "contain", "getContainingBlock", "currentNode", "getParentNode", "isLastTraversableNode", "CSS", "supports", "lastTraversableNodeNames", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "body", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "Object", "getPrototypeOf"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n"], "mappings": "AAAA,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACtC;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,CAACD,IAAI,CAAC,EAAE;IAChB,OAAO,CAACA,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO,WAAW;AACpB;AACA,SAASC,SAASA,CAACJ,IAAI,EAAE;EACvB,IAAIK,mBAAmB;EACvB,OAAO,CAACL,IAAI,IAAI,IAAI,IAAI,CAACK,mBAAmB,GAAGL,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,WAAW,KAAKT,MAAM;AAClI;AACA,SAASU,kBAAkBA,CAACR,IAAI,EAAE;EAChC,IAAIS,IAAI;EACR,OAAO,CAACA,IAAI,GAAG,CAACR,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACU,QAAQ,KAAKZ,MAAM,CAACY,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACE,eAAe;AAChI;AACA,SAASV,MAAMA,CAACW,KAAK,EAAE;EACrB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYC,IAAI,IAAID,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACC,IAAI;AACxE;AACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYG,OAAO,IAAIH,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACG,OAAO;AAC9E;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC5B,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYK,WAAW,IAAIL,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACK,WAAW;AACtF;AACA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B,IAAI,CAACf,SAAS,CAAC,CAAC,IAAI,OAAOsB,UAAU,KAAK,WAAW,EAAE;IACrD,OAAO,KAAK;EACd;EACA,OAAOP,KAAK,YAAYO,UAAU,IAAIP,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACO,UAAU;AACpF;AACA,MAAMC,4BAA4B,GAAG,aAAa,IAAIC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjF,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGC,gBAAgB,CAACL,OAAO,CAAC;EAC7B,OAAO,iCAAiC,CAACM,IAAI,CAACL,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC,IAAI,CAACL,4BAA4B,CAACU,GAAG,CAACH,OAAO,CAAC;AAC/H;AACA,MAAMI,aAAa,GAAG,aAAa,IAAIV,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjE,SAASW,cAAcA,CAACT,OAAO,EAAE;EAC/B,OAAOQ,aAAa,CAACD,GAAG,CAAC/B,WAAW,CAACwB,OAAO,CAAC,CAAC;AAChD;AACA,MAAMU,iBAAiB,GAAG,CAAC,eAAe,EAAE,QAAQ,CAAC;AACrD,SAASC,UAAUA,CAACX,OAAO,EAAE;EAC3B,OAAOU,iBAAiB,CAACE,IAAI,CAACC,QAAQ,IAAI;IACxC,IAAI;MACF,OAAOb,OAAO,CAACc,OAAO,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC,OAAOE,EAAE,EAAE;MACX,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ;AACA,MAAMC,mBAAmB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;AACxF,MAAMC,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,CAAC;AAC/F,MAAMC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC9D,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACvC,MAAMC,MAAM,GAAGC,QAAQ,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAGhC,SAAS,CAAC6B,YAAY,CAAC,GAAGf,gBAAgB,CAACe,YAAY,CAAC,GAAGA,YAAY;;EAEnF;EACA;EACA,OAAOJ,mBAAmB,CAACJ,IAAI,CAACvB,KAAK,IAAIkC,GAAG,CAAClC,KAAK,CAAC,GAAGkC,GAAG,CAAClC,KAAK,CAAC,KAAK,MAAM,GAAG,KAAK,CAAC,KAAKkC,GAAG,CAACC,aAAa,GAAGD,GAAG,CAACC,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACH,MAAM,KAAKE,GAAG,CAACE,cAAc,GAAGF,GAAG,CAACE,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAACJ,MAAM,KAAKE,GAAG,CAACG,MAAM,GAAGH,GAAG,CAACG,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAIT,gBAAgB,CAACL,IAAI,CAACvB,KAAK,IAAI,CAACkC,GAAG,CAACI,UAAU,IAAI,EAAE,EAAEC,QAAQ,CAACvC,KAAK,CAAC,CAAC,IAAI6B,aAAa,CAACN,IAAI,CAACvB,KAAK,IAAI,CAACkC,GAAG,CAACM,OAAO,IAAI,EAAE,EAAED,QAAQ,CAACvC,KAAK,CAAC,CAAC;AAC1a;AACA,SAASyC,kBAAkBA,CAAC9B,OAAO,EAAE;EACnC,IAAI+B,WAAW,GAAGC,aAAa,CAAChC,OAAO,CAAC;EACxC,OAAOP,aAAa,CAACsC,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACxE,IAAIZ,iBAAiB,CAACY,WAAW,CAAC,EAAE;MAClC,OAAOA,WAAW;IACpB,CAAC,MAAM,IAAIpB,UAAU,CAACoB,WAAW,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACAA,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;EAC1C;EACA,OAAO,IAAI;AACb;AACA,SAAST,QAAQA,CAAA,EAAG;EAClB,IAAI,OAAOY,GAAG,KAAK,WAAW,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,OAAO,KAAK;EAC7D,OAAOD,GAAG,CAACC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,MAAMC,wBAAwB,GAAG,aAAa,IAAItC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACpF,SAASmC,qBAAqBA,CAACxD,IAAI,EAAE;EACnC,OAAO2D,wBAAwB,CAAC7B,GAAG,CAAC/B,WAAW,CAACC,IAAI,CAAC,CAAC;AACxD;AACA,SAAS4B,gBAAgBA,CAACL,OAAO,EAAE;EACjC,OAAOnB,SAAS,CAACmB,OAAO,CAAC,CAACK,gBAAgB,CAACL,OAAO,CAAC;AACrD;AACA,SAASqC,aAAaA,CAACrC,OAAO,EAAE;EAC9B,IAAIT,SAAS,CAACS,OAAO,CAAC,EAAE;IACtB,OAAO;MACLsC,UAAU,EAAEtC,OAAO,CAACsC,UAAU;MAC9BC,SAAS,EAAEvC,OAAO,CAACuC;IACrB,CAAC;EACH;EACA,OAAO;IACLD,UAAU,EAAEtC,OAAO,CAACwC,OAAO;IAC3BD,SAAS,EAAEvC,OAAO,CAACyC;EACrB,CAAC;AACH;AACA,SAAST,aAAaA,CAACvD,IAAI,EAAE;EAC3B,IAAID,WAAW,CAACC,IAAI,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOA,IAAI;EACb;EACA,MAAMiE,MAAM;EACZ;EACAjE,IAAI,CAACkE,YAAY;EACjB;EACAlE,IAAI,CAACmE,UAAU;EACf;EACAjD,YAAY,CAAClB,IAAI,CAAC,IAAIA,IAAI,CAACoE,IAAI;EAC/B;EACA5D,kBAAkB,CAACR,IAAI,CAAC;EACxB,OAAOkB,YAAY,CAAC+C,MAAM,CAAC,GAAGA,MAAM,CAACG,IAAI,GAAGH,MAAM;AACpD;AACA,SAASI,0BAA0BA,CAACrE,IAAI,EAAE;EACxC,MAAMmE,UAAU,GAAGZ,aAAa,CAACvD,IAAI,CAAC;EACtC,IAAIwD,qBAAqB,CAACW,UAAU,CAAC,EAAE;IACrC,OAAOnE,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACM,aAAa,CAACgE,IAAI,GAAGtE,IAAI,CAACsE,IAAI;EACjE;EACA,IAAItD,aAAa,CAACmD,UAAU,CAAC,IAAI7C,iBAAiB,CAAC6C,UAAU,CAAC,EAAE;IAC9D,OAAOA,UAAU;EACnB;EACA,OAAOE,0BAA0B,CAACF,UAAU,CAAC;AAC/C;AACA,SAASI,oBAAoBA,CAACvE,IAAI,EAAEwE,IAAI,EAAEC,eAAe,EAAE;EACzD,IAAIC,oBAAoB;EACxB,IAAIF,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,MAAME,kBAAkB,GAAGN,0BAA0B,CAACrE,IAAI,CAAC;EAC3D,MAAM4E,MAAM,GAAGD,kBAAkB,MAAM,CAACD,oBAAoB,GAAG1E,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoE,oBAAoB,CAACJ,IAAI,CAAC;EAChI,MAAMO,GAAG,GAAGzE,SAAS,CAACuE,kBAAkB,CAAC;EACzC,IAAIC,MAAM,EAAE;IACV,MAAME,YAAY,GAAGC,eAAe,CAACF,GAAG,CAAC;IACzC,OAAOL,IAAI,CAACQ,MAAM,CAACH,GAAG,EAAEA,GAAG,CAACI,cAAc,IAAI,EAAE,EAAE3D,iBAAiB,CAACqD,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE,EAAEG,YAAY,IAAIL,eAAe,GAAGF,oBAAoB,CAACO,YAAY,CAAC,GAAG,EAAE,CAAC;EAC/L;EACA,OAAON,IAAI,CAACQ,MAAM,CAACL,kBAAkB,EAAEJ,oBAAoB,CAACI,kBAAkB,EAAE,EAAE,EAAEF,eAAe,CAAC,CAAC;AACvG;AACA,SAASM,eAAeA,CAACF,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACK,MAAM,IAAIC,MAAM,CAACC,cAAc,CAACP,GAAG,CAACK,MAAM,CAAC,GAAGL,GAAG,CAACC,YAAY,GAAG,IAAI;AAClF;AAEA,SAASlD,gBAAgB,EAAEyB,kBAAkB,EAAE7C,kBAAkB,EAAEuE,eAAe,EAAEV,0BAA0B,EAAEtE,WAAW,EAAE6D,aAAa,EAAEW,oBAAoB,EAAEhB,aAAa,EAAEnD,SAAS,EAAEsC,iBAAiB,EAAE5B,SAAS,EAAEE,aAAa,EAAEwC,qBAAqB,EAAEvD,MAAM,EAAEqB,iBAAiB,EAAEJ,YAAY,EAAEc,cAAc,EAAEE,UAAU,EAAEW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}