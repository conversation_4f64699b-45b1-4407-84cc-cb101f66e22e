{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Ship = createLucideIcon(\"Ship\", [[\"path\", {\n  d: \"M2 21c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1 .6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1\",\n  key: \"iegodh\"\n}], [\"path\", {\n  d: \"M19.38 20A11.6 11.6 0 0 0 21 14l-9-4-9 4c0 2.9.94 5.34 2.81 7.76\",\n  key: \"fp8vka\"\n}], [\"path\", {\n  d: \"M19 13V7a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v6\",\n  key: \"qpkstq\"\n}], [\"path\", {\n  d: \"M12 10v4\",\n  key: \"1kjpxc\"\n}], [\"path\", {\n  d: \"M12 2v3\",\n  key: \"qbqxhf\"\n}]]);\nexport { Ship as default };", "map": {"version": 3, "names": ["Ship", "createLucideIcon", "d", "key"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\ship.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Ship\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMWMuNi41IDEuMiAxIDIuNSAxIDIuNSAwIDIuNS0yIDUtMiAxLjMgMCAxLjkuNSAyLjUgMSAuNi41IDEuMiAxIDIuNSAxIDIuNSAwIDIuNS0yIDUtMiAxLjMgMCAxLjkuNSAyLjUgMSIgLz4KICA8cGF0aCBkPSJNMTkuMzggMjBBMTEuNiAxMS42IDAgMCAwIDIxIDE0bC05LTQtOSA0YzAgMi45Ljk0IDUuMzQgMi44MSA3Ljc2IiAvPgogIDxwYXRoIGQ9Ik0xOSAxM1Y3YTIgMiAwIDAgMC0yLTJIN2EyIDIgMCAwIDAtMiAydjYiIC8+CiAgPHBhdGggZD0iTTEyIDEwdjQiIC8+CiAgPHBhdGggZD0iTTEyIDJ2MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/ship\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ship = createLucideIcon('Ship', [\n  [\n    'path',\n    {\n      d: 'M2 21c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1 .6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1',\n      key: 'iegodh',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M19.38 20A11.6 11.6 0 0 0 21 14l-9-4-9 4c0 2.9.94 5.34 2.81 7.76',\n      key: 'fp8vka',\n    },\n  ],\n  ['path', { d: 'M19 13V7a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v6', key: 'qpkstq' }],\n  ['path', { d: 'M12 10v4', key: '1kjpxc' }],\n  ['path', { d: 'M12 2v3', key: 'qbqxhf' }],\n]);\n\nexport default Ship;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}