{"ast": null, "code": "import { createConfigUtils } from './config-utils.mjs';\nimport { mergeClassList } from './merge-classlist.mjs';\nimport { twJoin } from './tw-join.mjs';\nfunction createTailwindMerge() {\n  for (var _len = arguments.length, createConfig = new Array(_len), _key = 0; _key < _len; _key++) {\n    createConfig[_key] = arguments[_key];\n  }\n  var configUtils;\n  var cacheGet;\n  var cacheSet;\n  var functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    var firstCreateConfig = createConfig[0],\n      restCreateConfig = createConfig.slice(1);\n    var config = restCreateConfig.reduce(function (previousConfig, createConfigCurrent) {\n      return createConfigCurrent(previousConfig);\n    }, firstCreateConfig());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    var cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    var result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nexport { createTailwindMerge };", "map": {"version": 3, "names": ["createTailwindMerge", "_len", "arguments", "length", "createConfig", "Array", "_key", "configUtils", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "classList", "firstCreateConfig", "restCreateConfig", "slice", "config", "reduce", "previousConfig", "createConfigCurrent", "createConfigUtils", "cache", "get", "set", "tailwindMerge", "cachedResult", "result", "mergeClassList", "callTailwindMerge", "twJoin", "apply"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\create-tailwind-merge.ts"], "sourcesContent": ["import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { Config } from './types'\n\ntype CreateConfigFirst = () => Config\ntype CreateConfigSubsequent = (config: Config) => Config\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    ...createConfig: [CreateConfigFirst, ...CreateConfigSubsequent[]]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const [firstCreateConfig, ...restCreateConfig] = createConfig\n\n        const config = restCreateConfig.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            firstCreateConfig(),\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n"], "mappings": ";;;AAUgB,SAAAA,mBAAmBA,CAAA,EACkC;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAA9DC,YAA8D,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAA9DF,YAA8D,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEjE,IAAIC,WAAwB;EAC5B,IAAIC,QAAqC;EACzC,IAAIC,QAAqC;EACzC,IAAIC,cAAc,GAAGC,iBAAiB;EAEtC,SAASA,iBAAiBA,CAACC,SAAiB;IACxC,IAAOC,iBAAiB,GAAyBT,YAAY;MAAhCU,gBAAgB,GAAIV,YAAY,CAAAW,KAAA;IAE7D,IAAMC,MAAM,GAAGF,gBAAgB,CAACG,MAAM,CAClC,UAACC,cAAc,EAAEC,mBAAmB;MAAA,OAAKA,mBAAmB,CAACD,cAAc,CAAC;KAC5E,EAAAL,iBAAiB,EAAE,CACtB;IAEDN,WAAW,GAAGa,iBAAiB,CAACJ,MAAM,CAAC;IACvCR,QAAQ,GAAGD,WAAW,CAACc,KAAK,CAACC,GAAG;IAChCb,QAAQ,GAAGF,WAAW,CAACc,KAAK,CAACE,GAAG;IAChCb,cAAc,GAAGc,aAAa;IAE9B,OAAOA,aAAa,CAACZ,SAAS,CAAC;EACnC;EAEA,SAASY,aAAaA,CAACZ,SAAiB;IACpC,IAAMa,YAAY,GAAGjB,QAAQ,CAACI,SAAS,CAAC;IAExC,IAAIa,YAAY,EAAE;MACd,OAAOA,YAAY;IACtB;IAED,IAAMC,MAAM,GAAGC,cAAc,CAACf,SAAS,EAAEL,WAAW,CAAC;IACrDE,QAAQ,CAACG,SAAS,EAAEc,MAAM,CAAC;IAE3B,OAAOA,MAAM;EACjB;EAEA,OAAO,SAASE,iBAAiBA,CAAA;IAC7B,OAAOlB,cAAc,CAACmB,MAAM,CAACC,KAAK,CAAC,IAAI,EAAE5B,SAAgB,CAAC,CAAC;GAC9D;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}