{"ast": null, "code": "function fromTheme(key) {\n  var themeGetter = function themeGetter(theme) {\n    return theme[key] || [];\n  };\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n}\nexport { fromTheme };", "map": {"version": 3, "names": ["fromTheme", "key", "themeGetter", "theme", "isThemeGetter"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\from-theme.ts"], "sourcesContent": ["import { ThemeGetter, ThemeObject } from './types'\n\nexport function fromTheme(key: string): ThemeGetter {\n    const themeGetter = (theme: ThemeObject) => theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n"], "mappings": "AAEM,SAAUA,SAASA,CAACC,GAAW;EACjC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAkB;IAAA,OAAKA,KAAK,CAACF,GAAG,CAAC,IAAI,EAAE;EAAA;EAE5DC,WAAW,CAACE,aAAa,GAAG,IAAa;EAEzC,OAAOF,WAAW;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}