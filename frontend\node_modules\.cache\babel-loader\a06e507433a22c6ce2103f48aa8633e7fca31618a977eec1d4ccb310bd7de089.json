{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ScrollText = createLucideIcon(\"ScrollText\", [[\"path\", {\n  d: \"M8 21h12a2 2 0 0 0 2-2v-2H10v2a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v3h4\",\n  key: \"13a6an\"\n}], [\"path\", {\n  d: \"M19 17V5a2 2 0 0 0-2-2H4\",\n  key: \"zz82l3\"\n}], [\"path\", {\n  d: \"M15 8h-5\",\n  key: \"1khuty\"\n}], [\"path\", {\n  d: \"M15 12h-5\",\n  key: \"r7krc0\"\n}]]);\nexport { ScrollText as default };", "map": {"version": 3, "names": ["ScrollText", "createLucideIcon", "d", "key"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\scroll-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ScrollText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAyMWgxMmEyIDIgMCAwIDAgMi0ydi0ySDEwdjJhMiAyIDAgMSAxLTQgMFY1YTIgMiAwIDEgMC00IDB2M2g0IiAvPgogIDxwYXRoIGQ9Ik0xOSAxN1Y1YTIgMiAwIDAgMC0yLTJINCIgLz4KICA8cGF0aCBkPSJNMTUgOGgtNSIgLz4KICA8cGF0aCBkPSJNMTUgMTJoLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/scroll-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ScrollText = createLucideIcon('ScrollText', [\n  [\n    'path',\n    {\n      d: 'M8 21h12a2 2 0 0 0 2-2v-2H10v2a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v3h4',\n      key: '13a6an',\n    },\n  ],\n  ['path', { d: 'M19 17V5a2 2 0 0 0-2-2H4', key: 'zz82l3' }],\n  ['path', { d: 'M15 8h-5', key: '1khuty' }],\n  ['path', { d: 'M15 12h-5', key: 'r7krc0' }],\n]);\n\nexport default ScrollText;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}