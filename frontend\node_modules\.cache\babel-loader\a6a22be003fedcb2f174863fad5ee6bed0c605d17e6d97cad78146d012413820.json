{"ast": null, "code": "export var config = {\n  onError: function (e) {\n    return console.error(e);\n  }\n};\nexport var setConfig = function (conf) {\n  Object.assign(config, conf);\n};", "map": {"version": 3, "names": ["config", "onError", "e", "console", "error", "setConfig", "conf", "Object", "assign"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/use-sidecar/dist/es2015/config.js"], "sourcesContent": ["export var config = {\n    onError: function (e) { return console.error(e); },\n};\nexport var setConfig = function (conf) {\n    Object.assign(config, conf);\n};\n"], "mappings": "AAAA,OAAO,IAAIA,MAAM,GAAG;EAChBC,OAAO,EAAE,SAAAA,CAAUC,CAAC,EAAE;IAAE,OAAOC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EAAE;AACrD,CAAC;AACD,OAAO,IAAIG,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACnCC,MAAM,CAACC,MAAM,CAACR,MAAM,EAAEM,IAAI,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}