{"ast": null, "code": "import { createTailwindMerge } from './create-tailwind-merge.mjs';\nimport { getDefaultConfig } from './default-config.mjs';\nimport { mergeConfigs } from './merge-configs.mjs';\nfunction extendTailwindMerge(configExtension) {\n  for (var _len = arguments.length, createConfig = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    createConfig[_key - 1] = arguments[_key];\n  }\n  return typeof configExtension === 'function' ? createTailwindMerge.apply(void 0, [getDefaultConfig, configExtension].concat(createConfig)) : createTailwindMerge.apply(void 0, [function () {\n    return mergeConfigs(getDefaultConfig(), configExtension);\n  }].concat(createConfig));\n}\nexport { extendTailwindMerge };", "map": {"version": 3, "names": ["extendTailwindMerge", "configExtension", "_len", "arguments", "length", "createConfig", "Array", "_key", "createTailwindMerge", "apply", "getDefaultConfig", "concat", "mergeConfigs"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\extend-tailwind-merge.ts"], "sourcesContent": ["import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { Config } from './types'\n\ntype CreateConfigSubsequent = (config: Config) => Config\n\nexport function extendTailwindMerge(\n    configExtension: Partial<Config> | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) {\n    return typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n}\n"], "mappings": ";;;SAOgBA,mBAAmBA,CAC/BC,eAAyD,EAChB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAtCC,YAAsC,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAtCF,YAAsC,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEzC,OAAO,OAAON,eAAe,KAAK,UAAU,GACtCO,mBAAmB,CAAAC,KAAA,UAACC,gBAAgB,EAAET,eAAe,CAAK,CAAAU,MAAA,CAAAN,YAAY,CAAC,IACvEG,mBAAmB,CACfC,KAAA;IAAA,OAAMG,YAAY,CAACF,gBAAgB,EAAE,EAAET,eAAe,CAAC;EAAA,GAAAU,MAAA,CACpDN,YAAY,CAClB;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}