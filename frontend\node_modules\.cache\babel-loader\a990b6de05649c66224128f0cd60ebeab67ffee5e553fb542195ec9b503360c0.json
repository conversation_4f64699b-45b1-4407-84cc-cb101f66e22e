{"ast": null, "code": "import $45QHv$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $45QHv$forwardRef, useState as $45QHv$useState, useRef as $45QHv$useRef, useEffect as $45QHv$useEffect, useCallback as $45QHv$useCallback, createElement as $45QHv$createElement } from \"react\";\nimport { useComposedRefs as $45QHv$useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive as $45QHv$Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef as $45QHv$useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nconst $d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst $d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst $d3863c46a17e8a28$var$EVENT_OPTIONS = {\n  bubbles: false,\n  cancelable: true\n};\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\nconst $d3863c46a17e8a28$var$FOCUS_SCOPE_NAME = 'FocusScope';\nconst $d3863c46a17e8a28$export$20e40289641fbbb6 = /*#__PURE__*/$45QHv$forwardRef((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container1, setContainer] = $45QHv$useState(null);\n  const onMountAutoFocus = $45QHv$useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = $45QHv$useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = $45QHv$useRef(null);\n  const composedRefs = $45QHv$useComposedRefs(forwardedRef, node => setContainer(node));\n  const focusScope = $45QHv$useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    }\n  }).current; // Takes care of trapping focus if focus is moved outside programmatically for example\n  $45QHv$useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event) {\n        if (focusScope.paused || !container1) return;\n        const target = event.target;\n        if (container1.contains(target)) lastFocusedElementRef.current = target;else $d3863c46a17e8a28$var$focus(lastFocusedElementRef.current, {\n          select: true\n        });\n      }\n      function handleFocusOut(event) {\n        if (focusScope.paused || !container1) return;\n        const relatedTarget = event.relatedTarget; // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return; // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container1.contains(relatedTarget)) $d3863c46a17e8a28$var$focus(lastFocusedElementRef.current, {\n          select: true\n        });\n      } // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations) {\n        const focusedElement = document.activeElement;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) {\n            if (!(container1 !== null && container1 !== void 0 && container1.contains(focusedElement))) $d3863c46a17e8a28$var$focus(container1);\n          }\n        }\n      }\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container1) mutationObserver.observe(container1, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container1, focusScope.paused]);\n  $45QHv$useEffect(() => {\n    if (container1) {\n      $d3863c46a17e8a28$var$focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement;\n      const hasFocusedCandidate = container1.contains(previouslyFocusedElement);\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent($d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT, $d3863c46a17e8a28$var$EVENT_OPTIONS);\n        container1.addEventListener($d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container1.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          $d3863c46a17e8a28$var$focusFirst($d3863c46a17e8a28$var$removeLinks($d3863c46a17e8a28$var$getTabbableCandidates(container1)), {\n            select: true\n          });\n          if (document.activeElement === previouslyFocusedElement) $d3863c46a17e8a28$var$focus(container1);\n        }\n      }\n      return () => {\n        container1.removeEventListener($d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT, onMountAutoFocus); // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent($d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT, $d3863c46a17e8a28$var$EVENT_OPTIONS);\n          container1.addEventListener($d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container1.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) $d3863c46a17e8a28$var$focus(previouslyFocusedElement !== null && previouslyFocusedElement !== void 0 ? previouslyFocusedElement : document.body, {\n            select: true\n          });\n          // we need to remove the listener after we `dispatchEvent`\n          container1.removeEventListener($d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          $d3863c46a17e8a28$var$focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container1, onMountAutoFocus, onUnmountAutoFocus, focusScope]); // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = $45QHv$useCallback(event => {\n    if (!loop && !trapped) return;\n    if (focusScope.paused) return;\n    const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n    const focusedElement = document.activeElement;\n    if (isTabKey && focusedElement) {\n      const container = event.currentTarget;\n      const [first, last] = $d3863c46a17e8a28$var$getTabbableEdges(container);\n      const hasTabbableElementsInside = first && last; // we can only wrap focus if we have tabbable edges\n      if (!hasTabbableElementsInside) {\n        if (focusedElement === container) event.preventDefault();\n      } else {\n        if (!event.shiftKey && focusedElement === last) {\n          event.preventDefault();\n          if (loop) $d3863c46a17e8a28$var$focus(first, {\n            select: true\n          });\n        } else if (event.shiftKey && focusedElement === first) {\n          event.preventDefault();\n          if (loop) $d3863c46a17e8a28$var$focus(last, {\n            select: true\n          });\n        }\n      }\n    }\n  }, [loop, trapped, focusScope.paused]);\n  return /*#__PURE__*/$45QHv$createElement($45QHv$Primitive.div, $45QHv$babelruntimehelpersesmextends({\n    tabIndex: -1\n  }, scopeProps, {\n    ref: composedRefs,\n    onKeyDown: handleKeyDown\n  }));\n});\n/*#__PURE__*/\nObject.assign($d3863c46a17e8a28$export$20e40289641fbbb6, {\n  displayName: $d3863c46a17e8a28$var$FOCUS_SCOPE_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/ /**\n                                                                                                     * Attempts focusing the first element in a list of candidates.\n                                                                                                     * Stops when focus has actually moved.\n                                                                                                     */\nfunction $d3863c46a17e8a28$var$focusFirst(candidates, {\n  select = false\n} = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    $d3863c46a17e8a28$var$focus(candidate, {\n      select: select\n    });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction $d3863c46a17e8a28$var$getTabbableEdges(container) {\n  const candidates = $d3863c46a17e8a28$var$getTabbableCandidates(container);\n  const first = $d3863c46a17e8a28$var$findVisible(candidates, container);\n  const last = $d3863c46a17e8a28$var$findVisible(candidates.reverse(), container);\n  return [first, last];\n}\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction $d3863c46a17e8a28$var$getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: node => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP; // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode); // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction $d3863c46a17e8a28$var$findVisible(elements, container) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!$d3863c46a17e8a28$var$isHidden(element, {\n      upTo: container\n    })) return element;\n  }\n}\nfunction $d3863c46a17e8a28$var$isHidden(node, {\n  upTo: upTo\n}) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nfunction $d3863c46a17e8a28$var$isSelectableInput(element) {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\nfunction $d3863c46a17e8a28$var$focus(element, {\n  select = false\n} = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement; // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({\n      preventScroll: true\n    }); // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && $d3863c46a17e8a28$var$isSelectableInput(element) && select) element.select();\n  }\n}\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\nconst $d3863c46a17e8a28$var$focusScopesStack = $d3863c46a17e8a28$var$createFocusScopesStack();\nfunction $d3863c46a17e8a28$var$createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */let stack = [];\n  return {\n    add(focusScope) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) activeFocusScope === null || activeFocusScope === void 0 || activeFocusScope.pause();\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = $d3863c46a17e8a28$var$arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n    remove(focusScope) {\n      var _stack$;\n      stack = $d3863c46a17e8a28$var$arrayRemove(stack, focusScope);\n      (_stack$ = stack[0]) === null || _stack$ === void 0 || _stack$.resume();\n    }\n  };\n}\nfunction $d3863c46a17e8a28$var$arrayRemove(array, item) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) updatedArray.splice(index, 1);\n  return updatedArray;\n}\nfunction $d3863c46a17e8a28$var$removeLinks(items) {\n  return items.filter(item => item.tagName !== 'A');\n}\nconst $d3863c46a17e8a28$export$be92b6f5f03c0fe9 = $d3863c46a17e8a28$export$20e40289641fbbb6;\nexport { $d3863c46a17e8a28$export$20e40289641fbbb6 as FocusScope, $d3863c46a17e8a28$export$be92b6f5f03c0fe9 as Root };", "map": {"version": 3, "names": ["$d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT", "$d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT", "$d3863c46a17e8a28$var$EVENT_OPTIONS", "bubbles", "cancelable", "$d3863c46a17e8a28$var$FOCUS_SCOPE_NAME", "$d3863c46a17e8a28$export$20e40289641fbbb6", "$45QHv$forwardRef", "props", "forwardedRef", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container1", "<PERSON><PERSON><PERSON><PERSON>", "$45QHv$useState", "$45QHv$useCallbackRef", "lastFocusedElementRef", "$45QHv$useRef", "composedRefs", "$45QHv$useComposedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "$45QHv$useEffect", "handleFocusIn", "event", "target", "contains", "$d3863c46a17e8a28$var$focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "document", "activeElement", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "$d3863c46a17e8a28$var$focusScopesStack", "add", "previouslyFocusedElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "$d3863c46a17e8a28$var$focusFirst", "$d3863c46a17e8a28$var$removeLinks", "$d3863c46a17e8a28$var$getTabbableCandidates", "setTimeout", "unmountEvent", "body", "remove", "handleKeyDown", "$45QHv$useCallback", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "container", "currentTarget", "first", "last", "$d3863c46a17e8a28$var$getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "$45QHv$createElement", "$45QHv$Primitive", "div", "$45QHv$babelruntimehelpersesmextends", "tabIndex", "ref", "onKeyDown", "Object", "assign", "displayName", "focusFirst", "candidates", "candidate", "getTabbableEdges", "$d3863c46a17e8a28$var$findVisible", "reverse", "getTabbableCandidates", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "findVisible", "elements", "element", "$d3863c46a17e8a28$var$isHidden", "upTo", "isHidden", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "$d3863c46a17e8a28$var$isSelectableInput", "isSelectableInput", "HTMLInputElement", "focus", "preventScroll", "$d3863c46a17e8a28$var$createFocusScopesStack", "createFocusScopesStack", "stack", "activeFocusScope", "$d3863c46a17e8a28$var$arrayRemove", "unshift", "_stack$", "arrayRemove", "array", "item", "updatedArray", "index", "indexOf", "splice", "removeLinks", "items", "filter", "$d3863c46a17e8a28$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-focus-scope\\dist\\packages\\react\\focus-scope\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-focus-scope\\dist\\packages\\react\\focus-scope\\src\\FocusScope.tsx"], "sourcesContent": ["export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) {\n            if (!container?.contains(focusedElement)) focus(container);\n          }\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n"], "mappings": ";;;;;ACOA,MAAMA,wCAAkB,GAAG,6BAA3B;AACA,MAAMC,0CAAoB,GAAG,+BAA7B;AACA,MAAMC,mCAAa,GAAG;EAAEC,OAAO,EAAE,KAAX;EAAkBC,UAAU,EAAE;CAApD;AAIA;;;AAIA,MAAMC,sCAAgB,GAAG,YAAzB;AAgCA,MAAMC,yCAAU,gBAAGC,iBAAA,CAAqD,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAC/F,MAAM;IACJC,IAAI,GAAG,KADH;IAEJC,OAAO,GAAG,KAFN;IAGJC,gBAAgB,EAAEC,oBAHd;IAIJC,kBAAkB,EAAEC,sBAJhB;IAKJ,GAAGC;EAAH,CALI,GAMFR,KANJ;EAOA,MAAM,CAACS,UAAD,EAAYC,YAAZ,IAA4BC,eAAA,CAAmC,IAAnC,CAAlC;EACA,MAAMP,gBAAgB,GAAGQ,qBAAc,CAACP,oBAAD,CAAvC;EACA,MAAMC,kBAAkB,GAAGM,qBAAc,CAACL,sBAAD,CAAzC;EACA,MAAMM,qBAAqB,GAAGC,aAAA,CAAiC,IAAjC,CAA9B;EACA,MAAMC,YAAY,GAAGC,sBAAe,CAACf,YAAD,EAAgBgB,IAAD,IAAUP,YAAY,CAACO,IAAD,CAArC,CAApC;EAEA,MAAMC,UAAU,GAAGJ,aAAA,CAAa;IAC9BK,MAAM,EAAE,KADsB;IAE9BC,KAAKA,CAAA,EAAG;MACN,KAAKD,MAAL,GAAc,IAAd;KAH4B;IAK9BE,MAAMA,CAAA,EAAG;MACP,KAAKF,MAAL,GAAc,KAAd;;GANe,EAQhBG,OARH,CAd+F,CAwB/F;EACAC,gBAAA,CAAgB,MAAM;IACpB,IAAIpB,OAAJ,EAAa;MACX,SAASqB,aAATA,CAAuBC,KAAvB,EAA0C;QACxC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACV,UAA1B,EAAqC;QACrC,MAAMiB,MAAM,GAAGD,KAAK,CAACC,MAArB;QACA,IAAIjB,UAAS,CAACkB,QAAV,CAAmBD,MAAnB,CAAJ,EACEb,qBAAqB,CAACS,OAAtB,GAAgCI,MAAhC,MAEAE,2BAAK,CAACf,qBAAqB,CAACS,OAAvB,EAAgC;UAAEO,MAAM,EAAE;SAA1C,CAAL;;MAIJ,SAASC,cAATA,CAAwBL,KAAxB,EAA2C;QACzC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACV,UAA1B,EAAqC;QACrC,MAAMsB,aAAa,GAAGN,KAAK,CAACM,aAA5B,CAFyC,CAIzC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,aAAa,KAAK,IAAtB,EAA4B,OAda,CAgBzC;QACA;QACA,IAAI,CAACtB,UAAS,CAACkB,QAAV,CAAmBI,aAAnB,CAAL,EACEH,2BAAK,CAACf,qBAAqB,CAACS,OAAvB,EAAgC;UAAEO,MAAM,EAAE;SAA1C,CAAL;OA9BO,CAkCX;MACA;MACA;MACA,SAASG,eAATA,CAAyBC,SAAzB,EAAsD;QACpD,MAAMC,cAAc,GAAGC,QAAQ,CAACC,aAAhC;QACA,KAAK,MAAMC,QAAX,IAAuBJ,SAAvB,EAAkC;UAChC,IAAII,QAAQ,CAACC,YAAT,CAAsBC,MAAtB,GAA+B,CAAnC,EACE;YAAA,IAAI,EAAC9B,UAAD,aAACA,UAAD,eAACA,UAAS,CAAEkB,QAAX,CAAoBO,cAApB,CAAD,GAAsCN,2BAAK,CAACnB,UAAD,CAAL;;;;MAKhD0B,QAAQ,CAACK,gBAAT,CAA0B,SAA1B,EAAqChB,aAArC;MACAW,QAAQ,CAACK,gBAAT,CAA0B,UAA1B,EAAsCV,cAAtC;MACA,MAAMW,gBAAgB,GAAG,IAAIC,gBAAJ,CAAqBV,eAArB,CAAzB;MACA,IAAIvB,UAAJ,EAAegC,gBAAgB,CAACE,OAAjB,CAAyBlC,UAAzB,EAAoC;QAAEmC,SAAS,EAAE,IAAb;QAAmBC,OAAO,EAAE;OAAhE,CAAoC;MAEnD,OAAO,MAAM;QACXV,QAAQ,CAACW,mBAAT,CAA6B,SAA7B,EAAwCtB,aAAxC;QACAW,QAAQ,CAACW,mBAAT,CAA6B,UAA7B,EAAyChB,cAAzC;QACAW,gBAAgB,CAACM,UAAjB;OAHF;;GApDJ,EA0DG,CAAC5C,OAAD,EAAUM,UAAV,EAAqBS,UAAU,CAACC,MAAhC,CA1DH,CA0DC;EAEDI,gBAAA,CAAgB,MAAM;IACpB,IAAId,UAAJ,EAAe;MACbuC,sCAAgB,CAACC,GAAjB,CAAqB/B,UAArB;MACA,MAAMgC,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C;MACA,MAAMe,mBAAmB,GAAG1C,UAAS,CAACkB,QAAV,CAAmBuB,wBAAnB,CAA5B;MAEA,IAAI,CAACC,mBAAL,EAA0B;QACxB,MAAMC,UAAU,GAAG,IAAIC,WAAJ,CAAgB7D,wCAAhB,EAAoCE,mCAApC,CAAnB;QACAe,UAAS,CAAC+B,gBAAV,CAA2BhD,wCAA3B,EAA+CY,gBAA/C;QACAK,UAAS,CAAC6C,aAAV,CAAwBF,UAAxB;QACA,IAAI,CAACA,UAAU,CAACG,gBAAhB,EAAkC;UAChCC,gCAAU,CAACC,iCAAW,CAACC,2CAAqB,CAACjD,UAAD,CAAtB,CAAZ,EAAgD;YAAEoB,MAAM,EAAE;WAA1D,CAAV;UACA,IAAIM,QAAQ,CAACC,aAAT,KAA2Bc,wBAA/B,EACEtB,2BAAK,CAACnB,UAAD,CAAL;;;MAKN,OAAO,MAAM;QACXA,UAAS,CAACqC,mBAAV,CAA8BtD,wCAA9B,EAAkDY,gBAAlD,EADW,CAGX;QACA;QACA;QACAuD,UAAU,CAAC,MAAM;UACf,MAAMC,YAAY,GAAG,IAAIP,WAAJ,CAAgB5D,0CAAhB,EAAsCC,mCAAtC,CAArB;UACAe,UAAS,CAAC+B,gBAAV,CAA2B/C,0CAA3B,EAAiDa,kBAAjD;UACAG,UAAS,CAAC6C,aAAV,CAAwBM,YAAxB;UACA,IAAI,CAACA,YAAY,CAACL,gBAAlB,EACE3B,2BAAK,CAACsB,wBAAD,aAACA,wBAAD,cAACA,wBAAD,GAA6Bf,QAAQ,CAAC0B,IAAtC,EAA4C;YAAEhC,MAAM,EAAE;WAAtD,CAAL;UAEF;UACApB,UAAS,CAACqC,mBAAV,CAA8BrD,0CAA9B,EAAoDa,kBAApD;UAEA0C,sCAAgB,CAACc,MAAjB,CAAwB5C,UAAxB;SAVQ,EAWP,CAXO,CAAV;OANF;;GAlBJ,EAsCG,CAACT,UAAD,EAAYL,gBAAZ,EAA8BE,kBAA9B,EAAkDY,UAAlD,CAtCH,EArF+F,CA6H/F;EACA,MAAM6C,aAAa,GAAGC,kBAAA,CACnBvC,KAAD,IAAgC;IAC9B,IAAI,CAACvB,IAAD,IAAS,CAACC,OAAd,EAAuB;IACvB,IAAIe,UAAU,CAACC,MAAf,EAAuB;IAEvB,MAAM8C,QAAQ,GAAGxC,KAAK,CAACyC,GAAN,KAAc,KAAd,IAAuB,CAACzC,KAAK,CAAC0C,MAA9B,IAAwC,CAAC1C,KAAK,CAAC2C,OAA/C,IAA0D,CAAC3C,KAAK,CAAC4C,OAAlF;IACA,MAAMnC,cAAc,GAAGC,QAAQ,CAACC,aAAhC;IAEA,IAAI6B,QAAQ,IAAI/B,cAAhB,EAAgC;MAC9B,MAAMoC,SAAS,GAAG7C,KAAK,CAAC8C,aAAxB;MACA,MAAM,CAACC,KAAD,EAAQC,IAAR,IAAgBC,sCAAgB,CAACJ,SAAD,CAAtC;MACA,MAAMK,yBAAyB,GAAGH,KAAK,IAAIC,IAA3C,CAH8B,CAK9B;MACA,IAAI,CAACE,yBAAL,EACE;QAAA,IAAIzC,cAAc,KAAKoC,SAAvB,EAAkC7C,KAAK,CAACmD,cAAN,EAAlC;OAAA,MACK;QACL,IAAI,CAACnD,KAAK,CAACoD,QAAP,IAAmB3C,cAAc,KAAKuC,IAA1C,EAAgD;UAC9ChD,KAAK,CAACmD,cAAN;UACA,IAAI1E,IAAJ,EAAU0B,2BAAK,CAAC4C,KAAD,EAAQ;YAAE3C,MAAM,EAAE;WAAlB,CAAL;SAFZ,MAGO,IAAIJ,KAAK,CAACoD,QAAN,IAAkB3C,cAAc,KAAKsC,KAAzC,EAAgD;UACrD/C,KAAK,CAACmD,cAAN;UACA,IAAI1E,IAAJ,EAAU0B,2BAAK,CAAC6C,IAAD,EAAO;YAAE5C,MAAM,EAAE;WAAjB,CAAL;;;;GAtBE,EA2BpB,CAAC3B,IAAD,EAAOC,OAAP,EAAgBe,UAAU,CAACC,MAA3B,CA3BoB,CAAtB;EA8BA,oBACE2D,oBAAA,CAACC,gBAAD,CAAWC,GAAX,EADFC,oCAAA;IACiBC,QAAQ,EAAE;GAAzB,EAAiC1E,UAAjC;IAA6C2E,GAAG,EAAEpE,YAAlD;IAAgEqE,SAAS,EAAErB;GAA3E,EADF;CA5JiB,CAAnB;AAiKA;AAAAsB,MAAA,CAAAC,MAAA,CAAAxF,yCAAA;EAAAyF,WAAA,EAAA1F;CAAA;AAEA;;oGAAA,CAIA;;;;AAIA,SAAS2D,gCAATgC,CAAoBC,UAApB,EAA+C;EAAE5D,MAAM,GAAG;AAAT,CAAF,GAAqB,EAApE,EAAwE;EACtE,MAAMqB,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C;EACA,KAAK,MAAMsD,SAAX,IAAwBD,UAAxB,EAAoC;IAClC7D,2BAAK,CAAC8D,SAAD,EAAY;cAAE7D;KAAd,CAAL;IACA,IAAIM,QAAQ,CAACC,aAAT,KAA2Bc,wBAA/B,EAAyD;;;AAI7D;;;AAGA,SAASwB,sCAATiB,CAA0BrB,SAA1B,EAAkD;EAChD,MAAMmB,UAAU,GAAG/B,2CAAqB,CAACY,SAAD,CAAxC;EACA,MAAME,KAAK,GAAGoB,iCAAW,CAACH,UAAD,EAAanB,SAAb,CAAzB;EACA,MAAMG,IAAI,GAAGmB,iCAAW,CAACH,UAAU,CAACI,OAAX,EAAD,EAAuBvB,SAAvB,CAAxB;EACA,OAAO,CAACE,KAAD,EAAQC,IAAR,CAAP;;AAGF;;;;;;;;;;AAUA,SAASf,2CAAToC,CAA+BxB,SAA/B,EAAuD;EACrD,MAAMyB,KAAoB,GAAG,EAA7B;EACA,MAAMC,MAAM,GAAG7D,QAAQ,CAAC8D,gBAAT,CAA0B3B,SAA1B,EAAqC4B,UAAU,CAACC,YAAhD,EAA8D;IAC3EC,UAAU,EAAGnF,IAAD,IAAe;MACzB,MAAMoF,aAAa,GAAGpF,IAAI,CAACqF,OAAL,KAAiB,OAAjB,IAA4BrF,IAAI,CAACsF,IAAL,KAAc,QAAhE;MACA,IAAItF,IAAI,CAACuF,QAAL,IAAiBvF,IAAI,CAACwF,MAAtB,IAAgCJ,aAApC,EAAmD,OAAOH,UAAU,CAACQ,WAAlB,CAF1B,CAGzB;MACA;MACA;MACA,OAAOzF,IAAI,CAACiE,QAAL,IAAiB,CAAjB,GAAqBgB,UAAU,CAACS,aAAhC,GAAgDT,UAAU,CAACQ,WAAlE;;GAPW,CAAf;EAUA,OAAOV,MAAM,CAACY,QAAP,EAAP,EAA0Bb,KAAK,CAACc,IAAN,CAAWb,MAAM,CAACc,WAAlB,EAZ2B,CAarD;EACA;EACA,OAAOf,KAAP;;AAGF;;;;AAIA,SAASH,iCAATmB,CAAqBC,QAArB,EAA8C1C,SAA9C,EAAsE;EACpE,KAAK,MAAM2C,OAAX,IAAsBD,QAAtB,EAAgC;IAC9B;IACA,IAAI,CAACE,8BAAQ,CAACD,OAAD,EAAU;MAAEE,IAAI,EAAE7C;KAAlB,CAAb,EAA6C,OAAO2C,OAAP;;;AAIjD,SAASC,8BAATE,CAAkBnG,IAAlB,EAAqC;QAAEkG;AAAA,CAAvC,EAAuE;EACrE,IAAIE,gBAAgB,CAACpG,IAAD,CAAhB,CAAuBqG,UAAvB,KAAsC,QAA1C,EAAoD,OAAO,IAAP;EACpD,OAAOrG,IAAP,EAAa;IACX;IACA,IAAIkG,IAAI,KAAKI,SAAT,IAAsBtG,IAAI,KAAKkG,IAAnC,EAAyC,OAAO,KAAP;IACzC,IAAIE,gBAAgB,CAACpG,IAAD,CAAhB,CAAuBuG,OAAvB,KAAmC,MAAvC,EAA+C,OAAO,IAAP;IAC/CvG,IAAI,GAAGA,IAAI,CAACwG,aAAZ;;EAEF,OAAO,KAAP;;AAGF,SAASC,uCAATC,CAA2BV,OAA3B,EAA8F;EAC5F,OAAOA,OAAO,YAAYW,gBAAnB,IAAuC,YAAYX,OAA1D;;AAGF,SAASrF,2BAATiG,CAAeZ,OAAf,EAAiD;EAAEpF,MAAM,GAAG;AAAT,CAAF,GAAqB,EAAtE,EAA0E;EACxE;EACA,IAAIoF,OAAO,IAAIA,OAAO,CAACY,KAAvB,EAA8B;IAC5B,MAAM3E,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C,CAD4B,CAE5B;IACA6E,OAAO,CAACY,KAAR,CAAc;MAAEC,aAAa,EAAE;KAA/B,EAH4B,CAI5B;IACA,IAAIb,OAAO,KAAK/D,wBAAZ,IAAwCwE,uCAAiB,CAACT,OAAD,CAAzD,IAAsEpF,MAA1E,EACEoF,OAAO,CAACpF,MAAR,EADF;;;AAKJ;;;AAKA,MAAMmB,sCAAgB,GAAG+E,4CAAsB,EAA/C;AAEA,SAASA,4CAATC,CAAA,EAAkC;EAChC,8DACA,IAAIC,KAAsB,GAAG,EAA7B;EAEA,OAAO;IACLhF,GAAGA,CAAC/B,UAAD,EAA4B;MAC7B;MACA,MAAMgH,gBAAgB,GAAGD,KAAK,CAAC,CAAD,CAA9B;MACA,IAAI/G,UAAU,KAAKgH,gBAAnB,EACEA,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,IAAAA,gBAAgB,CAAE9G,KAAlB;MAEF;MACA6G,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQ/G,UAAR,CAAnB;MACA+G,KAAK,CAACG,OAAN,CAAclH,UAAd;KATG;IAYL4C,MAAMA,CAAC5C,UAAD,EAA4B;MAAA,IAAAmH,OAAA;MAChCJ,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQ/G,UAAR,CAAnB;MACA,CAAAmH,OAAA,GAAAJ,KAAK,CAAC,CAAD,CAAL,cAAAI,OAAA,eAAAA,OAAA,CAAUhH,MAAV;;GAdJ;;AAmBF,SAAS8G,iCAATG,CAAwBC,KAAxB,EAAoCC,IAApC,EAA6C;EAC3C,MAAMC,YAAY,GAAG,C,GAAIF,KAAJ,CAArB;EACA,MAAMG,KAAK,GAAGD,YAAY,CAACE,OAAb,CAAqBH,IAArB,CAAd;EACA,IAAIE,KAAK,KAAK,EAAd,EACED,YAAY,CAACG,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;EAEF,OAAOD,YAAP;;AAGF,SAAShF,iCAAToF,CAAqBC,KAArB,EAA2C;EACzC,OAAOA,KAAK,CAACC,MAAN,CAAcP,IAAD,IAAUA,IAAI,CAAClC,OAAL,KAAiB,GAAxC,CAAP;;AAGF,MAAM0C,yCAAI,GAAGlJ,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}