{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MessageSquarePlus = createLucideIcon(\"MessageSquarePlus\", [[\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n  key: \"1lielz\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"15\",\n  y1: \"10\",\n  y2: \"10\",\n  key: \"1lj1wd\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"7\",\n  y2: \"13\",\n  key: \"1cppfj\"\n}]]);\nexport { MessageSquarePlus as default };", "map": {"version": 3, "names": ["MessageSquarePlus", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\message-square-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageSquarePlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgogIDxsaW5lIHgxPSI5IiB4Mj0iMTUiIHkxPSIxMCIgeTI9IjEwIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iNyIgeTI9IjEzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquarePlus = createLucideIcon('MessageSquarePlus', [\n  [\n    'path',\n    {\n      d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z',\n      key: '1lielz',\n    },\n  ],\n  ['line', { x1: '9', x2: '15', y1: '10', y2: '10', key: '1lj1wd' }],\n  ['line', { x1: '12', x2: '12', y1: '7', y2: '13', key: '1cppfj' }],\n]);\n\nexport default MessageSquarePlus;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}