{"ast": null, "code": "var protectedDayOfYearTokens = ['D', 'DD'];\nvar protectedWeekYearTokens = ['YY', 'YYYY'];\nexport function isProtectedDayOfYearToken(token) {\n  return protectedDayOfYearTokens.indexOf(token) !== -1;\n}\nexport function isProtectedWeekYearToken(token) {\n  return protectedWeekYearTokens.indexOf(token) !== -1;\n}\nexport function throwProtectedError(token, format, input) {\n  if (token === 'YYYY') {\n    throw new RangeError(\"Use `yyyy` instead of `YYYY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'YY') {\n    throw new RangeError(\"Use `yy` instead of `YY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'D') {\n    throw new RangeError(\"Use `d` instead of `D` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'DD') {\n    throw new RangeError(\"Use `dd` instead of `DD` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  }\n}", "map": {"version": 3, "names": ["protectedDayOfYearTokens", "protectedWeekYearTokens", "isProtectedDayOfYearToken", "token", "indexOf", "isProtectedWeekYearToken", "throwProtectedError", "format", "input", "RangeError", "concat"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/date-fns/esm/_lib/protectedTokens/index.js"], "sourcesContent": ["var protectedDayOfYearTokens = ['D', 'DD'];\nvar protectedWeekYearTokens = ['YY', 'YYYY'];\nexport function isProtectedDayOfYearToken(token) {\n  return protectedDayOfYearTokens.indexOf(token) !== -1;\n}\nexport function isProtectedWeekYearToken(token) {\n  return protectedWeekYearTokens.indexOf(token) !== -1;\n}\nexport function throwProtectedError(token, format, input) {\n  if (token === 'YYYY') {\n    throw new RangeError(\"Use `yyyy` instead of `YYYY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'YY') {\n    throw new RangeError(\"Use `yy` instead of `YY` (in `\".concat(format, \"`) for formatting years to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'D') {\n    throw new RangeError(\"Use `d` instead of `D` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  } else if (token === 'DD') {\n    throw new RangeError(\"Use `dd` instead of `DD` (in `\".concat(format, \"`) for formatting days of the month to the input `\").concat(input, \"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\"));\n  }\n}"], "mappings": "AAAA,IAAIA,wBAAwB,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;AAC1C,IAAIC,uBAAuB,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;AAC5C,OAAO,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EAC/C,OAAOH,wBAAwB,CAACI,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AACvD;AACA,OAAO,SAASE,wBAAwBA,CAACF,KAAK,EAAE;EAC9C,OAAOF,uBAAuB,CAACG,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AACtD;AACA,OAAO,SAASG,mBAAmBA,CAACH,KAAK,EAAEI,MAAM,EAAEC,KAAK,EAAE;EACxD,IAAIL,KAAK,KAAK,MAAM,EAAE;IACpB,MAAM,IAAIM,UAAU,CAAC,oCAAoC,CAACC,MAAM,CAACH,MAAM,EAAE,wCAAwC,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EACrN,CAAC,MAAM,IAAIL,KAAK,KAAK,IAAI,EAAE;IACzB,MAAM,IAAIM,UAAU,CAAC,gCAAgC,CAACC,MAAM,CAACH,MAAM,EAAE,wCAAwC,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EACjN,CAAC,MAAM,IAAIL,KAAK,KAAK,GAAG,EAAE;IACxB,MAAM,IAAIM,UAAU,CAAC,8BAA8B,CAACC,MAAM,CAACH,MAAM,EAAE,oDAAoD,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EAC3N,CAAC,MAAM,IAAIL,KAAK,KAAK,IAAI,EAAE;IACzB,MAAM,IAAIM,UAAU,CAAC,gCAAgC,CAACC,MAAM,CAACH,MAAM,EAAE,oDAAoD,CAAC,CAACG,MAAM,CAACF,KAAK,EAAE,gFAAgF,CAAC,CAAC;EAC7N;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}