{"ast": null, "code": "import { useEffect as $hPSQ5$useEffect } from \"react\";\nimport { useCallbackRef as $hPSQ5$useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\n\n/**\n * Listens for when the escape key is down\n */\nfunction $addc16e1bbe58fd0$export$3a72a57244d6e765(onEscapeKeyDownProp, ownerDocument = globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) {\n  const onEscapeKeyDown = $hPSQ5$useCallbackRef(onEscapeKeyDownProp);\n  $hPSQ5$useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === 'Escape') onEscapeKeyDown(event);\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport { $addc16e1bbe58fd0$export$3a72a57244d6e765 as useEscapeKeydown };", "map": {"version": 3, "names": ["$addc16e1bbe58fd0$export$3a72a57244d6e765", "useEscapeKeydown", "onEscapeKeyDownProp", "ownerDocument", "globalThis", "document", "onEscapeKeyDown", "$hPSQ5$useCallbackRef", "$hPSQ5$useEffect", "handleKeyDown", "event", "key", "addEventListener", "removeEventListener"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-escape-keydown\\dist\\packages\\react\\use-escape-keydown\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-escape-keydown\\dist\\packages\\react\\use-escape-keydown\\src\\useEscapeKeydown.tsx"], "sourcesContent": ["export { useEscapeKeydown } from './useEscapeKeydown';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n"], "mappings": ";;;ACGA;;;AAGA,SAASA,yCAATC,CACEC,mBADF,EAEEC,aAAuB,GAAGC,UAAH,aAAGA,UAAH,uBAAGA,UAAU,CAAEC,QAFxC,EAGE;EACA,MAAMC,eAAe,GAAGC,qBAAc,CAACL,mBAAD,CAAtC;EAEAM,gBAAA,CAAgB,MAAM;IACpB,MAAMC,aAAa,GAAIC,KAAD,IAA0B;MAC9C,IAAIA,KAAK,CAACC,GAAN,KAAc,QAAlB,EACEL,eAAe,CAACI,KAAD,CAAf;KAFJ;IAKAP,aAAa,CAACS,gBAAd,CAA+B,SAA/B,EAA0CH,aAA1C;IACA,OAAO,MAAMN,aAAa,CAACU,mBAAd,CAAkC,SAAlC,EAA6CJ,aAA7C,CAAb;GAPF,EAQG,CAACH,eAAD,EAAkBH,aAAlB,CARH,CAQC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}