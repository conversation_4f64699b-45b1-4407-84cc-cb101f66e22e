{"ast": null, "code": "import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth };", "map": {"version": 3, "names": ["RemoveScrollBar", "zeroRightClassName", "fullWidthClassName", "noScrollbarsClassName", "removedBarSizeVariable", "getGapWidth"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/react-remove-scroll-bar/dist/es2015/index.js"], "sourcesContent": ["import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth, };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,SAASC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,QAAQ,aAAa;AACnH,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASL,eAAe,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}