{"ast": null, "code": "import $jbnEx$babelruntimehelpersesmextends from \"@babel/runtime/helpers/esm/extends\";\nimport { forwardRef as $jbnEx$forwardRef, createElement as $jbnEx$createElement } from \"react\";\nimport { Primitive as $jbnEx$Primitive } from \"@radix-ui/react-primitive\";\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\nconst $7e8f5cd07187803e$var$NAME = 'Arrow';\nconst $7e8f5cd07187803e$export$21b07c8f274aebd5 = /*#__PURE__*/$jbnEx$forwardRef((props, forwardedRef) => {\n  const {\n    children: children,\n    width = 10,\n    height = 5,\n    ...arrowProps\n  } = props;\n  return /*#__PURE__*/$jbnEx$createElement($jbnEx$Primitive.svg, $jbnEx$babelruntimehelpersesmextends({}, arrowProps, {\n    ref: forwardedRef,\n    width: width,\n    height: height,\n    viewBox: \"0 0 30 10\",\n    preserveAspectRatio: \"none\"\n  }), props.asChild ? children : /*#__PURE__*/$jbnEx$createElement(\"polygon\", {\n    points: \"0,0 30,0 15,10\"\n  }));\n});\n/*#__PURE__*/\nObject.assign($7e8f5cd07187803e$export$21b07c8f274aebd5, {\n  displayName: $7e8f5cd07187803e$var$NAME\n});\n/* -----------------------------------------------------------------------------------------------*/\nconst $7e8f5cd07187803e$export$be92b6f5f03c0fe9 = $7e8f5cd07187803e$export$21b07c8f274aebd5;\nexport { $7e8f5cd07187803e$export$21b07c8f274aebd5 as Arrow, $7e8f5cd07187803e$export$be92b6f5f03c0fe9 as Root };", "map": {"version": 3, "names": ["$7e8f5cd07187803e$var$NAME", "$7e8f5cd07187803e$export$21b07c8f274aebd5", "$jbnEx$forwardRef", "props", "forwardedRef", "children", "width", "height", "arrowProps", "$jbnEx$createElement", "$jbnEx$Primitive", "svg", "$jbnEx$babelruntimehelpersesmextends", "ref", "viewBox", "preserveAspectRatio", "<PERSON><PERSON><PERSON><PERSON>", "points", "Object", "assign", "displayName", "$7e8f5cd07187803e$export$be92b6f5f03c0fe9"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-arrow\\dist\\packages\\react\\arrow\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-arrow\\dist\\packages\\react\\arrow\\src\\Arrow.tsx"], "sourcesContent": ["export {\n  Arrow,\n  //\n  Root,\n} from './Arrow';\nexport type { ArrowProps } from './Arrow';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ElementRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = Radix.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "mappings": ";;;;ACKA;;;AAIA,MAAMA,0BAAI,GAAG,OAAb;AAMA,MAAMC,yCAAK,gBAAGC,iBAAA,CAA2C,CAACC,KAAD,EAAQC,YAAR,KAAyB;EAChF,MAAM;cAAEC,QAAF;IAAYC,KAAK,GAAG,EAApB;IAAwBC,MAAM,GAAG,CAAjC;IAAoC,GAAGC;EAAH,CAApC,GAAsDL,KAA5D;EACA,oBACEM,oBAAA,CAACC,gBAAD,CAAWC,GAAX,EAAAC,oCAAA,KACMJ,UADN,EADF;IAGIK,GAAG,EAAET,YAFP;IAGEE,KAAK,EAAEA,KAHT;IAIEC,MAAM,EAAEA,MAJV;IAKEO,OAAO,EAAC,WALV;IAMEC,mBAAmB,EAAC;GANtB,GASGZ,KAAK,CAACa,OAAN,GAAgBX,QAAhB,gBAA2BI,oBAT9B;IASuCQ,MAAM,EAAC;GAAhB,CAT9B,CADF;CAFY,CAAd;AAiBA;AAAAC,MAAA,CAAAC,MAAA,CAAAlB,yCAAA;EAAAmB,WAAA,EAAApB;CAAA;AAEA;AAEA,MAAMqB,yCAAI,GAAGpB,yCAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}