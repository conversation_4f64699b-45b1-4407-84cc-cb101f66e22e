{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n  return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n  var ref = React.useRef(null);\n  var _a = React.useState({\n      onScrollCapture: nothing,\n      onWheelCapture: nothing,\n      onTouchMoveCapture: nothing\n    }),\n    callbacks = _a[0],\n    setCallbacks = _a[1];\n  var forwardProps = props.forwardProps,\n    children = props.children,\n    className = props.className,\n    removeScrollBar = props.removeScrollBar,\n    enabled = props.enabled,\n    shards = props.shards,\n    sideCar = props.sideCar,\n    noIsolation = props.noIsolation,\n    inert = props.inert,\n    allowPinchZoom = props.allowPinchZoom,\n    _b = props.as,\n    Container = _b === void 0 ? 'div' : _b,\n    rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n  var SideCar = sideCar;\n  var containerRef = useMergeRefs([ref, parentRef]);\n  var containerProps = __assign(__assign({}, rest), callbacks);\n  return React.createElement(React.Fragment, null, enabled && React.createElement(SideCar, {\n    sideCar: effectCar,\n    removeScrollBar: removeScrollBar,\n    shards: shards,\n    noIsolation: noIsolation,\n    inert: inert,\n    setCallbacks: setCallbacks,\n    allowPinchZoom: !!allowPinchZoom,\n    lockRef: ref\n  }), forwardProps ? React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), {\n    ref: containerRef\n  })) : React.createElement(Container, __assign({}, containerProps, {\n    className: className,\n    ref: containerRef\n  }), children));\n});\nRemoveScroll.defaultProps = {\n  enabled: true,\n  removeScrollBar: true,\n  inert: false\n};\nRemoveScroll.classNames = {\n  fullWidth: fullWidthClassName,\n  zeroRight: zeroRightClassName\n};\nexport { RemoveScroll };", "map": {"version": 3, "names": ["__assign", "__rest", "React", "fullWidthClassName", "zeroRightClassName", "useMergeRefs", "effectCar", "nothing", "RemoveScroll", "forwardRef", "props", "parentRef", "ref", "useRef", "_a", "useState", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "callbacks", "setCallbacks", "forwardProps", "children", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noIsolation", "inert", "allowPinchZoom", "_b", "as", "Container", "rest", "SideCar", "containerRef", "containerProps", "createElement", "Fragment", "lockRef", "cloneElement", "Children", "only", "defaultProps", "classNames", "fullWidth", "zeroRight"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/UI.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,mCAAmC;AAC1F,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,SAAS,QAAQ,UAAU;AACpC,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;EACtB;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAIC,YAAY,GAAGN,KAAK,CAACO,UAAU,CAAC,UAAUC,KAAK,EAAEC,SAAS,EAAE;EAC5D,IAAIC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC5B,IAAIC,EAAE,GAAGZ,KAAK,CAACa,QAAQ,CAAC;MACpBC,eAAe,EAAET,OAAO;MACxBU,cAAc,EAAEV,OAAO;MACvBW,kBAAkB,EAAEX;IACxB,CAAC,CAAC;IAAEY,SAAS,GAAGL,EAAE,CAAC,CAAC,CAAC;IAAEM,YAAY,GAAGN,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAIO,YAAY,GAAGX,KAAK,CAACW,YAAY;IAAEC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IAAEC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAAEC,eAAe,GAAGd,KAAK,CAACc,eAAe;IAAEC,OAAO,GAAGf,KAAK,CAACe,OAAO;IAAEC,MAAM,GAAGhB,KAAK,CAACgB,MAAM;IAAEC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IAAEC,WAAW,GAAGlB,KAAK,CAACkB,WAAW;IAAEC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IAAEC,cAAc,GAAGpB,KAAK,CAACoB,cAAc;IAAEC,EAAE,GAAGrB,KAAK,CAACsB,EAAE;IAAEC,SAAS,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAAEG,IAAI,GAAGjC,MAAM,CAACS,KAAK,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;EACvgB,IAAIyB,OAAO,GAAGR,OAAO;EACrB,IAAIS,YAAY,GAAG/B,YAAY,CAAC,CAACO,GAAG,EAAED,SAAS,CAAC,CAAC;EACjD,IAAI0B,cAAc,GAAGrC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkC,IAAI,CAAC,EAAEf,SAAS,CAAC;EAC5D,OAAQjB,KAAK,CAACoC,aAAa,CAACpC,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAC5Cd,OAAO,IAAKvB,KAAK,CAACoC,aAAa,CAACH,OAAO,EAAE;IAAER,OAAO,EAAErB,SAAS;IAAEkB,eAAe,EAAEA,eAAe;IAAEE,MAAM,EAAEA,MAAM;IAAEE,WAAW,EAAEA,WAAW;IAAEC,KAAK,EAAEA,KAAK;IAAET,YAAY,EAAEA,YAAY;IAAEU,cAAc,EAAE,CAAC,CAACA,cAAc;IAAEU,OAAO,EAAE5B;EAAI,CAAC,CAAE,EACvOS,YAAY,GAAInB,KAAK,CAACuC,YAAY,CAACvC,KAAK,CAACwC,QAAQ,CAACC,IAAI,CAACrB,QAAQ,CAAC,EAAEtB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEqC,cAAc,CAAC,EAAE;IAAEzB,GAAG,EAAEwB;EAAa,CAAC,CAAC,CAAC,GAAKlC,KAAK,CAACoC,aAAa,CAACL,SAAS,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,cAAc,EAAE;IAAEd,SAAS,EAAEA,SAAS;IAAEX,GAAG,EAAEwB;EAAa,CAAC,CAAC,EAAEd,QAAQ,CAAE,CAAC;AAClQ,CAAC,CAAC;AACFd,YAAY,CAACoC,YAAY,GAAG;EACxBnB,OAAO,EAAE,IAAI;EACbD,eAAe,EAAE,IAAI;EACrBK,KAAK,EAAE;AACX,CAAC;AACDrB,YAAY,CAACqC,UAAU,GAAG;EACtBC,SAAS,EAAE3C,kBAAkB;EAC7B4C,SAAS,EAAE3C;AACf,CAAC;AACD,SAASI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}