{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MonitorSmartphone = createLucideIcon(\"MonitorSmartphone\", [[\"path\", {\n  d: \"M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8\",\n  key: \"10dyio\"\n}], [\"path\", {\n  d: \"M10 19v-3.96 3.15\",\n  key: \"1irgej\"\n}], [\"path\", {\n  d: \"M7 19h5\",\n  key: \"qswx4l\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"16\",\n  y: \"12\",\n  rx: \"2\",\n  key: \"1egngj\"\n}]]);\nexport { MonitorSmartphone as default };", "map": {"version": 3, "names": ["MonitorSmartphone", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\monitor-smartphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MonitorSmartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggOFY2YTIgMiAwIDAgMC0yLTJINGEyIDIgMCAwIDAtMiAydjdhMiAyIDAgMCAwIDIgMmg4IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOXYtMy45NiAzLjE1IiAvPgogIDxwYXRoIGQ9Ik03IDE5aDUiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjE2IiB5PSIxMiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/monitor-smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorSmartphone = createLucideIcon('MonitorSmartphone', [\n  [\n    'path',\n    {\n      d: 'M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8',\n      key: '10dyio',\n    },\n  ],\n  ['path', { d: 'M10 19v-3.96 3.15', key: '1irgej' }],\n  ['path', { d: 'M7 19h5', key: 'qswx4l' }],\n  [\n    'rect',\n    { width: '6', height: '10', x: '16', y: '12', rx: '2', key: '1egngj' },\n  ],\n]);\n\nexport default MonitorSmartphone;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACvE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}