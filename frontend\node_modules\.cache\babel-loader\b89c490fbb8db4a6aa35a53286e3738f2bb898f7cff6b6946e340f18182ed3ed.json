{"ast": null, "code": "function $e42e1063c40fb3ef$export$b9ecd428b558ff10(originalE<PERSON><PERSON><PERSON><PERSON>, ourEventHandler, {\n  checkForDefaultPrevented = true\n} = {}) {\n  return function handleEvent(event) {\n    originalEventHandler === null || originalEventHandler === void 0 || originalEventHandler(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) return ourEventHandler === null || ourEventHandler === void 0 ? void 0 : ourEventHandler(event);\n  };\n}\nexport { $e42e1063c40fb3ef$export$b9ecd428b558ff10 as composeEventHandlers };", "map": {"version": 3, "names": ["$e42e1063c40fb3ef$export$b9ecd428b558ff10", "composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "handleEvent", "event", "defaultPrevented"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\primitive\\dist\\packages\\core\\primitive\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\primitive\\dist\\packages\\core\\primitive\\src\\primitive.tsx"], "sourcesContent": ["export { composeEventHandlers } from './primitive';\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "mappings": "ACAA,SAASA,yCAATC,CACEC,oBADF,EAEEC,eAFF,EAGE;EAAEC,wBAAwB,GAAG;AAA3B,CAAF,GAAsC,EAHxC,EAIE;EACA,OAAO,SAASC,WAATA,CAAqBC,KAArB,EAA+B;IACpCJ,oBAAoB,SAApB,IAAAA,oBAAoB,WAApB,IAAAA,oBAAoB,CAAGI,KAAH,CAApB;IAEA,IAAIF,wBAAwB,KAAK,KAA7B,IAAsC,CAAGE,KAAF,CAA8BC,gBAAzE,EACE,OAAOJ,eAAP,aAAOA,eAAP,uBAAOA,eAAe,CAAGG,KAAH,CAAtB;GAJJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}