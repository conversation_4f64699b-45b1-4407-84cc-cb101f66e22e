{"ast": null, "code": "import * as $2AODx$react from \"react\";\nimport { useLayoutEffect as $2AODx$useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nconst $1746a345f3d73bb7$var$useReactId = $2AODx$react['useId'.toString()] || (() => undefined);\nlet $1746a345f3d73bb7$var$count = 0;\nfunction $1746a345f3d73bb7$export$f680877a34711e37(deterministicId) {\n  const [id, setId] = $2AODx$react.useState($1746a345f3d73bb7$var$useReactId()); // React versions older than 18 will have client-side ids only.\n  $2AODx$useLayoutEffect(() => {\n    if (!deterministicId) setId(reactId => reactId !== null && reactId !== void 0 ? reactId : String($1746a345f3d73bb7$var$count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\nexport { $1746a345f3d73bb7$export$f680877a34711e37 as useId };", "map": {"version": 3, "names": ["$1746a345f3d73bb7$var$useReactId", "$2AODx$react", "toString", "undefined", "$1746a345f3d73bb7$var$count", "$1746a345f3d73bb7$export$f680877a34711e37", "useId", "deterministicId", "id", "setId", "useState", "$2AODx$useLayoutEffect", "reactId", "String"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-id\\dist\\packages\\react\\id\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-id\\dist\\packages\\react\\id\\src\\id.tsx"], "sourcesContent": ["export { useId } from './id';\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We `toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)['useId'.toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n"], "mappings": ";;ACIA,MAAMA,gCAAU,GAAIC,YAAD,CAAe,QAAQC,QAAR,EAAf,MAAuC,MAAMC,SAA7C;AACnB,IAAIC,2BAAK,GAAG,CAAZ;AAEA,SAASC,yCAATC,CAAeC,eAAf,EAAiD;EAC/C,MAAM,CAACC,EAAD,EAAKC,KAAL,IAAcR,YAAK,CAACS,QAAN,CAAmCV,gCAAU,EAA7C,CAApB,CAD+C,CAE/C;EACAW,sBAAe,CAAC,MAAM;IACpB,IAAI,CAACJ,eAAL,EAAsBE,KAAK,CAAEG,OAAD,IAAaA,OAAb,aAAaA,OAAb,cAAaA,OAAb,GAAwBC,MAAM,CAACT,2BAAK,EAAN,CAA/B,CAAL;GADT,EAEZ,CAACG,eAAD,CAFY,CAAf;EAGA,OAAOA,eAAe,KAAKC,EAAE,GAAI,SAAQA,EAAG,EAAf,GAAmB,EAA1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}