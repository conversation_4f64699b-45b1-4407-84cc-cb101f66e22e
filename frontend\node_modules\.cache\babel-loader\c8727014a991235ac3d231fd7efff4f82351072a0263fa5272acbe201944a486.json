{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { ValueSetter } from \"./Setter.js\";\nexport var Parser = /*#__PURE__*/function () {\n  function Parser() {\n    _classCallCheck(this, Parser);\n    _defineProperty(this, \"incompatibleTokens\", void 0);\n    _defineProperty(this, \"priority\", void 0);\n    _defineProperty(this, \"subPriority\", void 0);\n  }\n  _createClass(Parser, [{\n    key: \"run\",\n    value: function run(dateString, token, match, options) {\n      var result = this.parse(dateString, token, match, options);\n      if (!result) {\n        return null;\n      }\n      return {\n        setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),\n        rest: result.rest\n      };\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_utcDate, _value, _options) {\n      return true;\n    }\n  }]);\n  return Parser;\n}();", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "ValueSetter", "<PERSON><PERSON><PERSON>", "key", "value", "run", "dateString", "token", "match", "options", "result", "parse", "setter", "validate", "set", "priority", "subPriority", "rest", "_utcDate", "_value", "_options"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/date-fns/esm/parse/_lib/Parser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { ValueSetter } from \"./Setter.js\";\nexport var Parser = /*#__PURE__*/function () {\n  function Parser() {\n    _classCallCheck(this, Parser);\n    _defineProperty(this, \"incompatibleTokens\", void 0);\n    _defineProperty(this, \"priority\", void 0);\n    _defineProperty(this, \"subPriority\", void 0);\n  }\n  _createClass(Parser, [{\n    key: \"run\",\n    value: function run(dateString, token, match, options) {\n      var result = this.parse(dateString, token, match, options);\n      if (!result) {\n        return null;\n      }\n      return {\n        setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),\n        rest: result.rest\n      };\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_utcDate, _value, _options) {\n      return true;\n    }\n  }]);\n  return Parser;\n}();"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAO,IAAIC,MAAM,GAAG,aAAa,YAAY;EAC3C,SAASA,MAAMA,CAAA,EAAG;IAChBJ,eAAe,CAAC,IAAI,EAAEI,MAAM,CAAC;IAC7BF,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;IACnDA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACzCA,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;EAC9C;EACAD,YAAY,CAACG,MAAM,EAAE,CAAC;IACpBC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;MACrD,IAAIC,MAAM,GAAG,IAAI,CAACC,KAAK,CAACL,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,CAAC;MAC1D,IAAI,CAACC,MAAM,EAAE;QACX,OAAO,IAAI;MACb;MACA,OAAO;QACLE,MAAM,EAAE,IAAIX,WAAW,CAACS,MAAM,CAACN,KAAK,EAAE,IAAI,CAACS,QAAQ,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC;QAC/FC,IAAI,EAAEP,MAAM,CAACO;MACf,CAAC;IACH;EACF,CAAC,EAAE;IACDd,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASS,QAAQA,CAACK,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAE;MACnD,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOlB,MAAM;AACf,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}