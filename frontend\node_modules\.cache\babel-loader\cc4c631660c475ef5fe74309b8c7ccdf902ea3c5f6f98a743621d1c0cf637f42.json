{"ast": null, "code": "import { useState as $9gyGR$useState } from \"react\";\nimport { useLayoutEffect as $9gyGR$useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction $db6c3485150b8e66$export$1ab7ae714698c4b8(element) {\n  const [size, setSize] = $9gyGR$useState(undefined);\n  $9gyGR$useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      });\n      const resizeObserver = new ResizeObserver(entries => {\n        if (!Array.isArray(entries)) return;\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) return;\n        const entry = entries[0];\n        let width;\n        let height;\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize']; // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({\n          width: width,\n          height: height\n        });\n      });\n      resizeObserver.observe(element, {\n        box: 'border-box'\n      });\n      return () => resizeObserver.unobserve(element);\n    } else\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n  }, [element]);\n  return size;\n}\nexport { $db6c3485150b8e66$export$1ab7ae714698c4b8 as useSize };", "map": {"version": 3, "names": ["$db6c3485150b8e66$export$1ab7ae714698c4b8", "useSize", "element", "size", "setSize", "$9gyGR$useState", "undefined", "$9gyGR$useLayoutEffect", "width", "offsetWidth", "height", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-size\\dist\\packages\\react\\use-size\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-size\\dist\\packages\\react\\use-size\\src\\useSize.tsx"], "sourcesContent": ["export { useSize } from './useSize';\n", "/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "mappings": ";;ACKA,SAASA,yCAATC,CAAiBC,OAAjB,EAA8C;EAC5C,MAAM,CAACC,IAAD,EAAOC,OAAP,IAAkBC,eAAA,CAA8DC,SAA9D,CAAxB;EAEAC,sBAAe,CAAC,MAAM;IACpB,IAAIL,OAAJ,EAAa;MACX;MACAE,OAAO,CAAC;QAAEI,KAAK,EAAEN,OAAO,CAACO,WAAjB;QAA8BC,MAAM,EAAER,OAAO,CAACS;OAA/C,CAAP;MAEA,MAAMC,cAAc,GAAG,IAAIC,cAAJ,CAAoBC,OAAD,IAAa;QACrD,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,OAAd,CAAL,EACE;QAGF;QACA;QACA,IAAI,CAACA,OAAO,CAACG,MAAb,EACE;QAGF,MAAMC,KAAK,GAAGJ,OAAO,CAAC,CAAD,CAArB;QACA,IAAIN,KAAJ;QACA,IAAIE,MAAJ;QAEA,IAAI,mBAAmBQ,KAAvB,EAA8B;UAC5B,MAAMC,eAAe,GAAGD,KAAK,CAAC,eAAD,CAA7B,CAD4B,CAE5B;UACA,MAAME,UAAU,GAAGL,KAAK,CAACC,OAAN,CAAcG,eAAd,IAAiCA,eAAe,CAAC,CAAD,CAAhD,GAAsDA,eAAzE;UACAX,KAAK,GAAGY,UAAU,CAAC,YAAD,CAAlB;UACAV,MAAM,GAAGU,UAAU,CAAC,WAAD,CAAnB;SALF,MAMO;UACL;UACA;UACAZ,KAAK,GAAGN,OAAO,CAACO,WAAhB;UACAC,MAAM,GAAGR,OAAO,CAACS,YAAjB;;QAGFP,OAAO,CAAC;UAzChBI,KAAA,EAyCkBA,KAAF;UAzChBE,MAAA,EAyCyBA;SAAV,CAAP;OA5BqB,CAAvB;MA+BAE,cAAc,CAACS,OAAf,CAAuBnB,OAAvB,EAAgC;QAAEoB,GAAG,EAAE;OAAvC,CAAgC;MAEhC,OAAO,MAAMV,cAAc,CAACW,SAAf,CAAyBrB,OAAzB,CAAb;KArCF;MAuCE;MACA;MACAE,OAAO,CAACE,SAAD,CAAP;GA1CW,EA4CZ,CAACJ,OAAD,CA5CY,CAAf;EA8CA,OAAOC,IAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}