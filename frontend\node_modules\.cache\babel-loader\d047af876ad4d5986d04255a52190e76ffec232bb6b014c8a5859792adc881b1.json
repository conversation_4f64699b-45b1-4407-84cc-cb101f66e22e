{"ast": null, "code": "var CLASS_PART_SEPARATOR = '-';\nfunction createClassUtils(config) {\n  var classMap = createClassMap(config);\n  var conflictingClassGroups = config.conflictingClassGroups,\n    _config$conflictingCl = config.conflictingClassGroupModifiers,\n    conflictingClassGroupModifiers = _config$conflictingCl === void 0 ? {} : _config$conflictingCl;\n  function getClassGroupId(className) {\n    var classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  }\n  function getConflictingClassGroupIds(classGroupId, hasPostfixModifier) {\n    var conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [].concat(conflicts, conflictingClassGroupModifiers[classGroupId]);\n    }\n    return conflicts;\n  }\n  return {\n    getClassGroupId: getClassGroupId,\n    getConflictingClassGroupIds: getConflictingClassGroupIds\n  };\n}\nfunction getGroupRecursive(classParts, classPartObject) {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  var currentClassPart = classParts[0];\n  var nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  var classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  var classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(function (_ref) {\n    var validator = _ref.validator;\n    return validator(classRest);\n  })?.classGroupId;\n}\nvar arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nfunction getGroupIdForArbitraryProperty(className) {\n  if (arbitraryPropertyRegex.test(className)) {\n    var arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    var property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n}\n/**\n * Exported for testing only\n */\nfunction createClassMap(config) {\n  var theme = config.theme,\n    prefix = config.prefix;\n  var classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  var prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(function (_ref2) {\n    var classGroupId = _ref2[0],\n      classGroup = _ref2[1];\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n}\nfunction processClassesRecursively(classGroup, classPartObject, classGroupId, theme) {\n  classGroup.forEach(function (classDefinition) {\n    if (typeof classDefinition === 'string') {\n      var classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId: classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(function (_ref3) {\n      var key = _ref3[0],\n        classGroup = _ref3[1];\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n}\nfunction getPart(classPartObject, path) {\n  var currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(function (pathPart) {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n}\nfunction isThemeGetter(func) {\n  return func.isThemeGetter;\n}\nfunction getPrefixedClassGroupEntries(classGroupEntries, prefix) {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(function (_ref4) {\n    var classGroupId = _ref4[0],\n      classGroup = _ref4[1];\n    var prefixedClassGroup = classGroup.map(function (classDefinition) {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(function (_ref5) {\n          var key = _ref5[0],\n            value = _ref5[1];\n          return [prefix + key, value];\n        }));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n}\nexport { createClassMap, createClassUtils };", "map": {"version": 3, "names": ["CLASS_PART_SEPARATOR", "createClassUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "_config$conflictingCl", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "concat", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "_ref", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "prefix", "Map", "prefixedClassGroupEntries", "getPrefixedClassGroupEntries", "Object", "entries", "classGroups", "for<PERSON>ach", "_ref2", "classGroup", "processClassesRecursively", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "_ref3", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "classGroupEntries", "map", "_ref4", "prefixedClassGroup", "fromEntries", "_ref5", "value"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\class-utils.ts"], "sourcesContent": ["import { ClassGroup, ClassGroupId, ClassValidator, Config, ThemeGetter, ThemeObject } from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: ClassGroupId\n}\n\ninterface ClassValidatorObject {\n    classGroupId: ClassGroupId\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport function createClassUtils(config: Config) {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers = {} } = config\n\n    function getClassGroupId(className: string) {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    function getConflictingClassGroupIds(classGroupId: ClassGroupId, hasPostfixModifier: boolean) {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nfunction getGroupRecursive(\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): ClassGroupId | undefined {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nfunction getGroupIdForArbitraryProperty(className: string) {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport function createClassMap(config: Config) {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nfunction processClassesRecursively(\n    classGroup: ClassGroup,\n    classPartObject: ClassPartObject,\n    classGroupId: ClassGroupId,\n    theme: ThemeObject,\n) {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nfunction getPart(classPartObject: ClassPartObject, path: string) {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nfunction isThemeGetter(func: ClassValidator | ThemeGetter): func is ThemeGetter {\n    return (func as ThemeGetter).isThemeGetter\n}\n\nfunction getPrefixedClassGroupEntries(\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup]> {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n"], "mappings": "AAaA,IAAMA,oBAAoB,GAAG,GAAG;AAE1B,SAAUC,gBAAgBA,CAACC,MAAc;EAC3C,IAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC;EACvC,IAAQG,sBAAsB,GAA0CH,MAAM,CAAtEG,sBAAsB;IAAAC,qBAA0C,GAAAJ,MAAM,CAA9CK,8BAA8B;IAA9BA,8BAA8B,GAAAD,qBAAA,cAAG,EAAE,GAAAA,qBAAA;EAEnE,SAASE,eAAeA,CAACC,SAAiB;IACtC,IAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACX,oBAAoB,CAAC;IAExD;IACA,IAAIU,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;MACjDF,UAAU,CAACG,KAAK,EAAE;IACrB;IAED,OAAOC,iBAAiB,CAACJ,UAAU,EAAEP,QAAQ,CAAC,IAAIY,8BAA8B,CAACN,SAAS,CAAC;EAC/F;EAEA,SAASO,2BAA2BA,CAACC,YAA0B,EAAEC,kBAA2B;IACxF,IAAMC,SAAS,GAAGd,sBAAsB,CAACY,YAAY,CAAC,IAAI,EAAE;IAE5D,IAAIC,kBAAkB,IAAIX,8BAA8B,CAACU,YAAY,CAAC,EAAE;MACpE,UAAAG,MAAA,CAAWD,SAAS,EAAKZ,8BAA8B,CAACU,YAAY,CAAE;IACzE;IAED,OAAOE,SAAS;EACpB;EAEA,OAAO;IACHX,eAAe,EAAfA,eAAe;IACfQ,2BAA2B,EAA3BA;GACH;AACL;AAEA,SAASF,iBAAiBA,CACtBJ,UAAoB,EACpBW,eAAgC;EAEhC,IAAIX,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IACzB,OAAOS,eAAe,CAACJ,YAAY;EACtC;EAED,IAAMK,gBAAgB,GAAGZ,UAAU,CAAC,CAAC,CAAE;EACvC,IAAMa,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC;EAC1E,IAAMI,2BAA2B,GAAGH,mBAAmB,GACjDT,iBAAiB,CAACJ,UAAU,CAACiB,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAC,GAC3DK,SAAS;EAEf,IAAIF,2BAA2B,EAAE;IAC7B,OAAOA,2BAA2B;EACrC;EAED,IAAIL,eAAe,CAACQ,UAAU,CAACjB,MAAM,KAAK,CAAC,EAAE;IACzC,OAAOgB,SAAS;EACnB;EAED,IAAME,SAAS,GAAGpB,UAAU,CAACqB,IAAI,CAAC/B,oBAAoB,CAAC;EAEvD,OAAOqB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,UAAAC,IAAA;IAAA,IAAGC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAA,OAAOA,SAAS,CAACJ,SAAS,CAAC;EAAA,EAAC,EAAEb,YAAY;AACjG;AAEA,IAAMkB,sBAAsB,GAAG,YAAY;AAE3C,SAASpB,8BAA8BA,CAACN,SAAiB;EACrD,IAAI0B,sBAAsB,CAACC,IAAI,CAAC3B,SAAS,CAAC,EAAE;IACxC,IAAM4B,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC7B,SAAS,CAAE,CAAC,CAAC,CAAC;IAC7E,IAAM8B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C;IAED,IAAIF,QAAQ,EAAE;MACV;MACA,OAAO,aAAa,GAAGA,QAAQ;IAClC;EACJ;AACL;AAEA;;AAEG;AACG,SAAUnC,cAAcA,CAACF,MAAc;EACzC,IAAQwC,KAAK,GAAaxC,MAAM,CAAxBwC,KAAK;IAAEC,MAAM,GAAKzC,MAAM,CAAjByC,MAAM;EACrB,IAAMxC,QAAQ,GAAoB;IAC9BqB,QAAQ,EAAE,IAAIoB,GAAG,EAA2B;IAC5Cf,UAAU,EAAE;GACf;EAED,IAAMgB,yBAAyB,GAAGC,4BAA4B,CAC1DC,MAAM,CAACC,OAAO,CAAC9C,MAAM,CAAC+C,WAAW,CAAC,EAClCN,MAAM,CACT;EAEDE,yBAAyB,CAACK,OAAO,CAAC,UAA+BC,KAAA;IAAA,IAA7BlC,YAAY,GAAAkC,KAAA;MAAEC,UAAU,GAAAD,KAAA;IACxDE,yBAAyB,CAACD,UAAU,EAAEjD,QAAQ,EAAEc,YAAY,EAAEyB,KAAK,CAAC;EACxE,CAAC,CAAC;EAEF,OAAOvC,QAAQ;AACnB;AAEA,SAASkD,yBAAyBA,CAC9BD,UAAsB,EACtB/B,eAAgC,EAChCJ,YAA0B,EAC1ByB,KAAkB;EAElBU,UAAU,CAACF,OAAO,CAAC,UAACI,eAAe,EAAI;IACnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;MACrC,IAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAGjC,eAAe,GAAGmC,OAAO,CAACnC,eAAe,EAAEiC,eAAe,CAAC;MACxFC,qBAAqB,CAACtC,YAAY,GAAGA,YAAY;MACjD;IACH;IAED,IAAI,OAAOqC,eAAe,KAAK,UAAU,EAAE;MACvC,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;QAChCD,yBAAyB,CACrBC,eAAe,CAACZ,KAAK,CAAC,EACtBrB,eAAe,EACfJ,YAAY,EACZyB,KAAK,CACR;QACD;MACH;MAEDrB,eAAe,CAACQ,UAAU,CAAC6B,IAAI,CAAC;QAC5BxB,SAAS,EAAEoB,eAAe;QAC1BrC,YAAY,EAAZA;MACH,EAAC;MAEF;IACH;IAED8B,MAAM,CAACC,OAAO,CAACM,eAAe,CAAC,CAACJ,OAAO,CAAC,UAAsBS,KAAA;MAAA,IAApBC,GAAG,GAAAD,KAAA;QAAEP,UAAU,GAAAO,KAAA;MACrDN,yBAAyB,CACrBD,UAAU,EACVI,OAAO,CAACnC,eAAe,EAAEuC,GAAG,CAAC,EAC7B3C,YAAY,EACZyB,KAAK,CACR;IACL,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAASc,OAAOA,CAACnC,eAAgC,EAAEwC,IAAY;EAC3D,IAAIC,sBAAsB,GAAGzC,eAAe;EAE5CwC,IAAI,CAAClD,KAAK,CAACX,oBAAoB,CAAC,CAACkD,OAAO,CAAC,UAACa,QAAQ,EAAI;IAClD,IAAI,CAACD,sBAAsB,CAACtC,QAAQ,CAACwC,GAAG,CAACD,QAAQ,CAAC,EAAE;MAChDD,sBAAsB,CAACtC,QAAQ,CAACyC,GAAG,CAACF,QAAQ,EAAE;QAC1CvC,QAAQ,EAAE,IAAIoB,GAAG,EAAE;QACnBf,UAAU,EAAE;MACf,EAAC;IACL;IAEDiC,sBAAsB,GAAGA,sBAAsB,CAACtC,QAAQ,CAACC,GAAG,CAACsC,QAAQ,CAAE;EAC3E,CAAC,CAAC;EAEF,OAAOD,sBAAsB;AACjC;AAEA,SAASL,aAAaA,CAACS,IAAkC;EACrD,OAAQA,IAAoB,CAACT,aAAa;AAC9C;AAEA,SAASX,4BAA4BA,CACjCqB,iBAAwE,EACxExB,MAA0B;EAE1B,IAAI,CAACA,MAAM,EAAE;IACT,OAAOwB,iBAAiB;EAC3B;EAED,OAAOA,iBAAiB,CAACC,GAAG,CAAC,UAA+BC,KAAA;IAAA,IAA7BpD,YAAY,GAAAoD,KAAA;MAAEjB,UAAU,GAAAiB,KAAA;IACnD,IAAMC,kBAAkB,GAAGlB,UAAU,CAACgB,GAAG,CAAC,UAACd,eAAe,EAAI;MAC1D,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;QACrC,OAAOX,MAAM,GAAGW,eAAe;MAClC;MAED,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;QACrC,OAAOP,MAAM,CAACwB,WAAW,CACrBxB,MAAM,CAACC,OAAO,CAACM,eAAe,CAAC,CAACc,GAAG,CAAC,UAAAI,KAAA;UAAA,IAAEZ,GAAG,GAAAY,KAAA;YAAEC,KAAK,GAAAD,KAAA;UAAA,OAAM,CAAC7B,MAAM,GAAGiB,GAAG,EAAEa,KAAK,CAAC;QAAA,EAAC,CAC/E;MACJ;MAED,OAAOnB,eAAe;IAC1B,CAAC,CAAC;IAEF,OAAO,CAACrC,YAAY,EAAEqD,kBAAkB,CAAC;EAC7C,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}