{"ast": null, "code": "import { createContext as $7Gjcd$createContext, createElement as $7Gjcd$createElement, useContext as $7Gjcd$useContext } from \"react\";\nconst $f631663db3294ace$var$DirectionContext = /*#__PURE__*/$7Gjcd$createContext(undefined);\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\nconst $f631663db3294ace$export$c760c09fdd558351 = props => {\n  const {\n    dir: dir,\n    children: children\n  } = props;\n  return /*#__PURE__*/$7Gjcd$createElement($f631663db3294ace$var$DirectionContext.Provider, {\n    value: dir\n  }, children);\n};\n/* -----------------------------------------------------------------------------------------------*/\nfunction $f631663db3294ace$export$b39126d51d94e6f3(localDir) {\n  const globalDir = $7Gjcd$useContext($f631663db3294ace$var$DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\nconst $f631663db3294ace$export$2881499e37b75b9a = $f631663db3294ace$export$c760c09fdd558351;\nexport { $f631663db3294ace$export$b39126d51d94e6f3 as useDirection, $f631663db3294ace$export$2881499e37b75b9a as Provider, $f631663db3294ace$export$c760c09fdd558351 as DirectionProvider };", "map": {"version": 3, "names": ["$f631663db3294ace$var$DirectionContext", "$7Gjcd$createContext", "undefined", "$f631663db3294ace$export$c760c09fdd558351", "props", "dir", "children", "$7Gjcd$createElement", "Provider", "value", "$f631663db3294ace$export$b39126d51d94e6f3", "useDirection", "localDir", "globalDir", "$7Gjcd$useContext", "$f631663db3294ace$export$2881499e37b75b9a"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-direction\\dist\\packages\\react\\direction\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-direction\\dist\\packages\\react\\direction\\src\\Direction.tsx"], "sourcesContent": ["export {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n} from './Direction';\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "mappings": ";ACGA,MAAMA,sCAAgB,gBAAGC,oBAAA,CAA2CC,SAA3C,CAAzB;AAEA;;;AAQA,MAAMC,yCAAmD,GAAIC,KAAD,IAAW;EACrE,MAAM;IAdRC,GAAA,EAcUA,GAAF;IAdRC,QAAA,EAceA;EAAA,CAAP,GAAoBF,KAA1B;EACA,oBAAOG,oBAAA,CAACP,sCAAD,CAAkBQ,QAAlB,EAAP;IAAkCC,KAAK,EAAEJ;GAAlC,EAAwCC,QAAxC,CAAP;CAFF;AAKA;AAEA,SAASI,yCAATC,CAAsBC,QAAtB,EAA4C;EAC1C,MAAMC,SAAS,GAAGC,iBAAA,CAAiBd,sCAAjB,CAAlB;EACA,OAAOY,QAAQ,IAAIC,SAAZ,IAAyB,KAAhC;;AAGF,MAAME,yCAAQ,GAAGZ,yCAAjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}