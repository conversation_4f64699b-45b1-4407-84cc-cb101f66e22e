{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Truck = createLucideIcon(\"Truck\", [[\"path\", {\n  d: \"M10 17h4V5H2v12h3\",\n  key: \"1jq12e\"\n}], [\"path\", {\n  d: \"M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5\",\n  key: \"1xb3ft\"\n}], [\"path\", {\n  d: \"M14 17h1\",\n  key: \"nufu4t\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"a7aife\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"2.5\",\n  key: \"1mdrzq\"\n}]]);\nexport { Truck as default };", "map": {"version": 3, "names": ["Truck", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\truck.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Truck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTdoNFY1SDJ2MTJoMyIgLz4KICA8cGF0aCBkPSJNMjAgMTdoMnYtMy4zNGE0IDQgMCAwIDAtMS4xNy0yLjgzTDE5IDloLTUiIC8+CiAgPHBhdGggZD0iTTE0IDE3aDEiIC8+CiAgPGNpcmNsZSBjeD0iNy41IiBjeT0iMTcuNSIgcj0iMi41IiAvPgogIDxjaXJjbGUgY3g9IjE3LjUiIGN5PSIxNy41IiByPSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/truck\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Truck = createLucideIcon('Truck', [\n  ['path', { d: 'M10 17h4V5H2v12h3', key: '1jq12e' }],\n  ['path', { d: 'M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5', key: '1xb3ft' }],\n  ['path', { d: 'M14 17h1', key: 'nufu4t' }],\n  ['circle', { cx: '7.5', cy: '17.5', r: '2.5', key: 'a7aife' }],\n  ['circle', { cx: '17.5', cy: '17.5', r: '2.5', key: '1mdrzq' }],\n]);\n\nexport default Truck;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,GAC7D,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOH,GAAK;AAAA,CAAU,EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}