{"ast": null, "code": "var getDefaultParent = function (originalTarget) {\n  if (typeof document === 'undefined') {\n    return null;\n  }\n  var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n  return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n  return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n  return targets.map(function (target) {\n    if (parent.contains(target)) {\n      return target;\n    }\n    var correctedTarget = unwrapHost(target);\n    if (correctedTarget && parent.contains(correctedTarget)) {\n      return correctedTarget;\n    }\n    console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n    return null;\n  }).filter(function (x) {\n    return Boolean(x);\n  });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n  var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  var markerCounter = markerMap[markerName];\n  var hiddenNodes = [];\n  var elementsToKeep = new Set();\n  var elementsToStop = new Set(targets);\n  var keep = function (el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    keep(el.parentNode);\n  };\n  targets.forEach(keep);\n  var deep = function (parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    Array.prototype.forEach.call(parent.children, function (node) {\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        try {\n          var attr = node.getAttribute(controlAttribute);\n          var alreadyHidden = attr !== null && attr !== 'false';\n          var counterValue = (counterMap.get(node) || 0) + 1;\n          var markerValue = (markerCounter.get(node) || 0) + 1;\n          counterMap.set(node, counterValue);\n          markerCounter.set(node, markerValue);\n          hiddenNodes.push(node);\n          if (counterValue === 1 && alreadyHidden) {\n            uncontrolledNodes.set(node, true);\n          }\n          if (markerValue === 1) {\n            node.setAttribute(markerName, 'true');\n          }\n          if (!alreadyHidden) {\n            node.setAttribute(controlAttribute, 'true');\n          }\n        } catch (e) {\n          console.error('aria-hidden: cannot operate on ', node, e);\n        }\n      }\n    });\n  };\n  deep(parentNode);\n  elementsToKeep.clear();\n  lockCount++;\n  return function () {\n    hiddenNodes.forEach(function (node) {\n      var counterValue = counterMap.get(node) - 1;\n      var markerValue = markerCounter.get(node) - 1;\n      counterMap.set(node, counterValue);\n      markerCounter.set(node, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledNodes.has(node)) {\n          node.removeAttribute(controlAttribute);\n        }\n        uncontrolledNodes.delete(node);\n      }\n      if (!markerValue) {\n        node.removeAttribute(markerName);\n      }\n    });\n    lockCount--;\n    if (!lockCount) {\n      // clear\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledNodes = new WeakMap();\n      markerMap = {};\n    }\n  };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-aria-hidden';\n  }\n  var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n  var activeParentNode = parentNode || getDefaultParent(originalTarget);\n  if (!activeParentNode) {\n    return function () {\n      return null;\n    };\n  }\n  // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n  // and script elements, as they have no impact on accessibility.\n  targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n  return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-inert-ed';\n  }\n  var activeParentNode = parentNode || getDefaultParent(originalTarget);\n  if (!activeParentNode) {\n    return function () {\n      return null;\n    };\n  }\n  return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n  return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-suppressed';\n  }\n  return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};", "map": {"version": 3, "names": ["getDefaultParent", "originalTarget", "document", "sampleTarget", "Array", "isArray", "ownerDocument", "body", "counterMap", "WeakMap", "uncontrolledNodes", "markerMap", "lockCount", "unwrapHost", "node", "host", "parentNode", "correctTargets", "parent", "targets", "map", "target", "contains", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "filter", "x", "Boolean", "applyAttributeToOthers", "markerName", "controlAttribute", "markerCounter", "hiddenNodes", "elementsToKeep", "Set", "elementsToStop", "keep", "el", "has", "add", "for<PERSON>ach", "deep", "prototype", "call", "children", "attr", "getAttribute", "alreadyHidden", "counterValue", "get", "markerValue", "set", "push", "setAttribute", "e", "clear", "removeAttribute", "delete", "hideOthers", "from", "activeParentNode", "apply", "querySelectorAll", "inertOthers", "supportsInert", "HTMLElement", "hasOwnProperty", "suppressOthers"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/aria-hidden/dist/es2015/index.js"], "sourcesContent": ["var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAG,SAAAA,CAAUC,cAAc,EAAE;EAC7C,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc;EACrF,OAAOE,YAAY,CAACG,aAAa,CAACC,IAAI;AAC1C,CAAC;AACD,IAAIC,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC9B,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,IAAIE,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAKA,IAAI,CAACC,IAAI,IAAIF,UAAU,CAACC,IAAI,CAACE,UAAU,CAAC,CAAC;AAC7D,CAAC;AACD,IAAIC,cAAc,GAAG,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;EAC5C,OAAOA,OAAO,CACTC,GAAG,CAAC,UAAUC,MAAM,EAAE;IACvB,IAAIH,MAAM,CAACI,QAAQ,CAACD,MAAM,CAAC,EAAE;MACzB,OAAOA,MAAM;IACjB;IACA,IAAIE,eAAe,GAAGV,UAAU,CAACQ,MAAM,CAAC;IACxC,IAAIE,eAAe,IAAIL,MAAM,CAACI,QAAQ,CAACC,eAAe,CAAC,EAAE;MACrD,OAAOA,eAAe;IAC1B;IACAC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEJ,MAAM,EAAE,yBAAyB,EAAEH,MAAM,EAAE,iBAAiB,CAAC;IAC1F,OAAO,IAAI;EACf,CAAC,CAAC,CACGQ,MAAM,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOC,OAAO,CAACD,CAAC,CAAC;EAAE,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,sBAAsB,GAAG,SAAAA,CAAU5B,cAAc,EAAEe,UAAU,EAAEc,UAAU,EAAEC,gBAAgB,EAAE;EAC7F,IAAIZ,OAAO,GAAGF,cAAc,CAACD,UAAU,EAAEZ,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC,CAAC;EAC3G,IAAI,CAACU,SAAS,CAACmB,UAAU,CAAC,EAAE;IACxBnB,SAAS,CAACmB,UAAU,CAAC,GAAG,IAAIrB,OAAO,CAAC,CAAC;EACzC;EACA,IAAIuB,aAAa,GAAGrB,SAAS,CAACmB,UAAU,CAAC;EACzC,IAAIG,WAAW,GAAG,EAAE;EACpB,IAAIC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,IAAIC,cAAc,GAAG,IAAID,GAAG,CAAChB,OAAO,CAAC;EACrC,IAAIkB,IAAI,GAAG,SAAAA,CAAUC,EAAE,EAAE;IACrB,IAAI,CAACA,EAAE,IAAIJ,cAAc,CAACK,GAAG,CAACD,EAAE,CAAC,EAAE;MAC/B;IACJ;IACAJ,cAAc,CAACM,GAAG,CAACF,EAAE,CAAC;IACtBD,IAAI,CAACC,EAAE,CAACtB,UAAU,CAAC;EACvB,CAAC;EACDG,OAAO,CAACsB,OAAO,CAACJ,IAAI,CAAC;EACrB,IAAIK,IAAI,GAAG,SAAAA,CAAUxB,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,IAAIkB,cAAc,CAACG,GAAG,CAACrB,MAAM,CAAC,EAAE;MACvC;IACJ;IACAd,KAAK,CAACuC,SAAS,CAACF,OAAO,CAACG,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,EAAE,UAAU/B,IAAI,EAAE;MAC1D,IAAIoB,cAAc,CAACK,GAAG,CAACzB,IAAI,CAAC,EAAE;QAC1B4B,IAAI,CAAC5B,IAAI,CAAC;MACd,CAAC,MACI;QACD,IAAI;UACA,IAAIgC,IAAI,GAAGhC,IAAI,CAACiC,YAAY,CAAChB,gBAAgB,CAAC;UAC9C,IAAIiB,aAAa,GAAGF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,OAAO;UACrD,IAAIG,YAAY,GAAG,CAACzC,UAAU,CAAC0C,GAAG,CAACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;UAClD,IAAIqC,WAAW,GAAG,CAACnB,aAAa,CAACkB,GAAG,CAACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;UACpDN,UAAU,CAAC4C,GAAG,CAACtC,IAAI,EAAEmC,YAAY,CAAC;UAClCjB,aAAa,CAACoB,GAAG,CAACtC,IAAI,EAAEqC,WAAW,CAAC;UACpClB,WAAW,CAACoB,IAAI,CAACvC,IAAI,CAAC;UACtB,IAAImC,YAAY,KAAK,CAAC,IAAID,aAAa,EAAE;YACrCtC,iBAAiB,CAAC0C,GAAG,CAACtC,IAAI,EAAE,IAAI,CAAC;UACrC;UACA,IAAIqC,WAAW,KAAK,CAAC,EAAE;YACnBrC,IAAI,CAACwC,YAAY,CAACxB,UAAU,EAAE,MAAM,CAAC;UACzC;UACA,IAAI,CAACkB,aAAa,EAAE;YAChBlC,IAAI,CAACwC,YAAY,CAACvB,gBAAgB,EAAE,MAAM,CAAC;UAC/C;QACJ,CAAC,CACD,OAAOwB,CAAC,EAAE;UACN/B,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEX,IAAI,EAAEyC,CAAC,CAAC;QAC7D;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACDb,IAAI,CAAC1B,UAAU,CAAC;EAChBkB,cAAc,CAACsB,KAAK,CAAC,CAAC;EACtB5C,SAAS,EAAE;EACX,OAAO,YAAY;IACfqB,WAAW,CAACQ,OAAO,CAAC,UAAU3B,IAAI,EAAE;MAChC,IAAImC,YAAY,GAAGzC,UAAU,CAAC0C,GAAG,CAACpC,IAAI,CAAC,GAAG,CAAC;MAC3C,IAAIqC,WAAW,GAAGnB,aAAa,CAACkB,GAAG,CAACpC,IAAI,CAAC,GAAG,CAAC;MAC7CN,UAAU,CAAC4C,GAAG,CAACtC,IAAI,EAAEmC,YAAY,CAAC;MAClCjB,aAAa,CAACoB,GAAG,CAACtC,IAAI,EAAEqC,WAAW,CAAC;MACpC,IAAI,CAACF,YAAY,EAAE;QACf,IAAI,CAACvC,iBAAiB,CAAC6B,GAAG,CAACzB,IAAI,CAAC,EAAE;UAC9BA,IAAI,CAAC2C,eAAe,CAAC1B,gBAAgB,CAAC;QAC1C;QACArB,iBAAiB,CAACgD,MAAM,CAAC5C,IAAI,CAAC;MAClC;MACA,IAAI,CAACqC,WAAW,EAAE;QACdrC,IAAI,CAAC2C,eAAe,CAAC3B,UAAU,CAAC;MACpC;IACJ,CAAC,CAAC;IACFlB,SAAS,EAAE;IACX,IAAI,CAACA,SAAS,EAAE;MACZ;MACAJ,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;MAC1BD,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;MAC1BC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;MACjCE,SAAS,GAAG,CAAC,CAAC;IAClB;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgD,UAAU,GAAG,SAAAA,CAAU1D,cAAc,EAAEe,UAAU,EAAEc,UAAU,EAAE;EACtE,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,kBAAkB;EAAE;EAC9D,IAAIX,OAAO,GAAGf,KAAK,CAACwD,IAAI,CAACxD,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC,CAAC;EAC3F,IAAI4D,gBAAgB,GAAG7C,UAAU,IAAIhB,gBAAgB,CAACC,cAAc,CAAC;EACrE,IAAI,CAAC4D,gBAAgB,EAAE;IACnB,OAAO,YAAY;MAAE,OAAO,IAAI;IAAE,CAAC;EACvC;EACA;EACA;EACA1C,OAAO,CAACkC,IAAI,CAACS,KAAK,CAAC3C,OAAO,EAAEf,KAAK,CAACwD,IAAI,CAACC,gBAAgB,CAACE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAAC;EACjG,OAAOlC,sBAAsB,CAACV,OAAO,EAAE0C,gBAAgB,EAAE/B,UAAU,EAAE,aAAa,CAAC;AACvF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIkC,WAAW,GAAG,SAAAA,CAAU/D,cAAc,EAAEe,UAAU,EAAEc,UAAU,EAAE;EACvE,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,eAAe;EAAE;EAC3D,IAAI+B,gBAAgB,GAAG7C,UAAU,IAAIhB,gBAAgB,CAACC,cAAc,CAAC;EACrE,IAAI,CAAC4D,gBAAgB,EAAE;IACnB,OAAO,YAAY;MAAE,OAAO,IAAI;IAAE,CAAC;EACvC;EACA,OAAOhC,sBAAsB,CAAC5B,cAAc,EAAE4D,gBAAgB,EAAE/B,UAAU,EAAE,OAAO,CAAC;AACxF,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAImC,aAAa,GAAG,SAAAA,CAAA,EAAY;EACnC,OAAO,OAAOC,WAAW,KAAK,WAAW,IAAIA,WAAW,CAACvB,SAAS,CAACwB,cAAc,CAAC,OAAO,CAAC;AAC9F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUnE,cAAc,EAAEe,UAAU,EAAEc,UAAU,EAAE;EAC1E,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,iBAAiB;EAAE;EAC7D,OAAO,CAACmC,aAAa,CAAC,CAAC,GAAGD,WAAW,GAAGL,UAAU,EAAE1D,cAAc,EAAEe,UAAU,EAAEc,UAAU,CAAC;AAC/F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}