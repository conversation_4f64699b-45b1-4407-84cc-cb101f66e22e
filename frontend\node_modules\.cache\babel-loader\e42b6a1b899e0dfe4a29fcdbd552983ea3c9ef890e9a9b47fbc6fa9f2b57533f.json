{"ast": null, "code": "var arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nvar fractionRegex = /^\\d+\\/\\d+$/;\nvar stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nvar tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nvar lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\n// Shadow always begins with x and y offset separated by underscore\nvar shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nfunction isLength(value) {\n  return isNumber(value) || stringLengths.has(value) || fractionRegex.test(value) || isArbitraryLength(value);\n}\nfunction isArbitraryLength(value) {\n  return getIsArbitraryValue(value, 'length', isLengthOnly);\n}\nfunction isArbitrarySize(value) {\n  return getIsArbitraryValue(value, 'size', isNever);\n}\nfunction isArbitraryPosition(value) {\n  return getIsArbitraryValue(value, 'position', isNever);\n}\nfunction isArbitraryUrl(value) {\n  return getIsArbitraryValue(value, 'url', isUrl);\n}\nfunction isArbitraryNumber(value) {\n  return getIsArbitraryValue(value, 'number', isNumber);\n}\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nvar isArbitraryWeight = isArbitraryNumber;\nfunction isNumber(value) {\n  return !Number.isNaN(Number(value));\n}\nfunction isPercent(value) {\n  return value.endsWith('%') && isNumber(value.slice(0, -1));\n}\nfunction isInteger(value) {\n  return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly);\n}\nfunction isArbitraryValue(value) {\n  return arbitraryValueRegex.test(value);\n}\nfunction isAny() {\n  return true;\n}\nfunction isTshirtSize(value) {\n  return tshirtUnitRegex.test(value);\n}\nfunction isArbitraryShadow(value) {\n  return getIsArbitraryValue(value, '', isShadow);\n}\nfunction getIsArbitraryValue(value, label, testValue) {\n  var result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return result[1] === label;\n    }\n    return testValue(result[2]);\n  }\n  return false;\n}\nfunction isLengthOnly(value) {\n  return lengthUnitRegex.test(value);\n}\nfunction isNever() {\n  return false;\n}\nfunction isUrl(value) {\n  return value.startsWith('url(');\n}\nfunction isIntegerOnly(value) {\n  return Number.isInteger(Number(value));\n}\nfunction isShadow(value) {\n  return shadowRegex.test(value);\n}\nexport { isAny, isArbitraryLength, isArbitraryNumber, isArbitraryPosition, isArbitraryShadow, isArbitrarySize, isArbitraryUrl, isArbitraryValue, isArbitraryWeight, isInteger, isLength, isNumber, isPercent, isTshirtSize };", "map": {"version": 3, "names": ["arbitraryValueRegex", "fractionRegex", "stringLengths", "Set", "tshirtUnitRegex", "lengthUnitRegex", "shadowRegex", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNumber", "has", "test", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isArbitrarySize", "isNever", "isArbitraryPosition", "isArbitraryUrl", "isUrl", "isArbitraryNumber", "isArbitraryWeight", "Number", "isNaN", "isPercent", "endsWith", "slice", "isInteger", "isIntegerOnly", "isArbitraryValue", "isAny", "isTshirtSize", "isArbitraryShadow", "is<PERSON><PERSON>ow", "label", "testValue", "result", "exec", "startsWith"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\tailwind-merge\\src\\lib\\validators.ts"], "sourcesContent": ["const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\n// Shadow always begins with x and y offset separated by underscore\nconst shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\n\nexport function isLength(value: string) {\n    return (\n        isNumber(value) ||\n        stringLengths.has(value) ||\n        fractionRegex.test(value) ||\n        isArbitraryLength(value)\n    )\n}\n\nexport function isArbitraryLength(value: string) {\n    return getIsArbitraryValue(value, 'length', isLengthOnly)\n}\n\nexport function isArbitrarySize(value: string) {\n    return getIsArbitraryValue(value, 'size', isNever)\n}\n\nexport function isArbitraryPosition(value: string) {\n    return getIsArbitraryValue(value, 'position', isNever)\n}\n\nexport function isArbitraryUrl(value: string) {\n    return getIsArbitraryValue(value, 'url', isUrl)\n}\n\nexport function isArbitraryNumber(value: string) {\n    return getIsArbitraryValue(value, 'number', isNumber)\n}\n\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nexport const isArbitraryWeight = isArbitraryNumber\n\nexport function isNumber(value: string) {\n    return !Number.isNaN(Number(value))\n}\n\nexport function isPercent(value: string) {\n    return value.endsWith('%') && isNumber(value.slice(0, -1))\n}\n\nexport function isInteger(value: string) {\n    return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly)\n}\n\nexport function isArbitraryValue(value: string) {\n    return arbitraryValueRegex.test(value)\n}\n\nexport function isAny() {\n    return true\n}\n\nexport function isTshirtSize(value: string) {\n    return tshirtUnitRegex.test(value)\n}\n\nexport function isArbitraryShadow(value: string) {\n    return getIsArbitraryValue(value, '', isShadow)\n}\n\nfunction getIsArbitraryValue(value: string, label: string, testValue: (value: string) => boolean) {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return result[1] === label\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nfunction isLengthOnly(value: string) {\n    return lengthUnitRegex.test(value)\n}\n\nfunction isNever() {\n    return false\n}\n\nfunction isUrl(value: string) {\n    return value.startsWith('url(')\n}\n\nfunction isIntegerOnly(value: string) {\n    return Number.isInteger(Number(value))\n}\n\nfunction isShadow(value: string) {\n    return shadowRegex.test(value)\n}\n"], "mappings": "AAAA,IAAMA,mBAAmB,GAAG,4BAA4B;AACxD,IAAMC,aAAa,GAAG,YAAY;AAClC,IAAMC,aAAa,gBAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvD,IAAMC,eAAe,GAAG,kCAAkC;AAC1D,IAAMC,eAAe,GACjB,2HAA2H;AAC/H;AACA,IAAMC,WAAW,GAAG,wDAAwD;AAEtE,SAAUC,QAAQA,CAACC,KAAa;EAClC,OACIC,QAAQ,CAACD,KAAK,CAAC,IACfN,aAAa,CAACQ,GAAG,CAACF,KAAK,CAAC,IACxBP,aAAa,CAACU,IAAI,CAACH,KAAK,CAAC,IACzBI,iBAAiB,CAACJ,KAAK,CAAC;AAEhC;AAEM,SAAUI,iBAAiBA,CAACJ,KAAa;EAC3C,OAAOK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEM,YAAY,CAAC;AAC7D;AAEM,SAAUC,eAAeA,CAACP,KAAa;EACzC,OAAOK,mBAAmB,CAACL,KAAK,EAAE,MAAM,EAAEQ,OAAO,CAAC;AACtD;AAEM,SAAUC,mBAAmBA,CAACT,KAAa;EAC7C,OAAOK,mBAAmB,CAACL,KAAK,EAAE,UAAU,EAAEQ,OAAO,CAAC;AAC1D;AAEM,SAAUE,cAAcA,CAACV,KAAa;EACxC,OAAOK,mBAAmB,CAACL,KAAK,EAAE,KAAK,EAAEW,KAAK,CAAC;AACnD;AAEM,SAAUC,iBAAiBA,CAACZ,KAAa;EAC3C,OAAOK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEC,QAAQ,CAAC;AACzD;AAEA;;AAEG;AACI,IAAMY,iBAAiB,GAAGD,iBAAA;AAE3B,SAAUX,QAAQA,CAACD,KAAa;EAClC,OAAO,CAACc,MAAM,CAACC,KAAK,CAACD,MAAM,CAACd,KAAK,CAAC,CAAC;AACvC;AAEM,SAAUgB,SAASA,CAAChB,KAAa;EACnC,OAAOA,KAAK,CAACiB,QAAQ,CAAC,GAAG,CAAC,IAAIhB,QAAQ,CAACD,KAAK,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9D;AAEM,SAAUC,SAASA,CAACnB,KAAa;EACnC,OAAOoB,aAAa,CAACpB,KAAK,CAAC,IAAIK,mBAAmB,CAACL,KAAK,EAAE,QAAQ,EAAEoB,aAAa,CAAC;AACtF;AAEM,SAAUC,gBAAgBA,CAACrB,KAAa;EAC1C,OAAOR,mBAAmB,CAACW,IAAI,CAACH,KAAK,CAAC;AAC1C;SAEgBsB,KAAKA,CAAA;EACjB,OAAO,IAAI;AACf;AAEM,SAAUC,YAAYA,CAACvB,KAAa;EACtC,OAAOJ,eAAe,CAACO,IAAI,CAACH,KAAK,CAAC;AACtC;AAEM,SAAUwB,iBAAiBA,CAACxB,KAAa;EAC3C,OAAOK,mBAAmB,CAACL,KAAK,EAAE,EAAE,EAAEyB,QAAQ,CAAC;AACnD;AAEA,SAASpB,mBAAmBA,CAACL,KAAa,EAAE0B,KAAa,EAAEC,SAAqC;EAC5F,IAAMC,MAAM,GAAGpC,mBAAmB,CAACqC,IAAI,CAAC7B,KAAK,CAAC;EAE9C,IAAI4B,MAAM,EAAE;IACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;MACX,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAKF,KAAK;IAC7B;IAED,OAAOC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC;EAC/B;EAED,OAAO,KAAK;AAChB;AAEA,SAAStB,YAAYA,CAACN,KAAa;EAC/B,OAAOH,eAAe,CAACM,IAAI,CAACH,KAAK,CAAC;AACtC;AAEA,SAASQ,OAAOA,CAAA;EACZ,OAAO,KAAK;AAChB;AAEA,SAASG,KAAKA,CAACX,KAAa;EACxB,OAAOA,KAAK,CAAC8B,UAAU,CAAC,MAAM,CAAC;AACnC;AAEA,SAASV,aAAaA,CAACpB,KAAa;EAChC,OAAOc,MAAM,CAACK,SAAS,CAACL,MAAM,CAACd,KAAK,CAAC,CAAC;AAC1C;AAEA,SAASyB,QAAQA,CAACzB,KAAa;EAC3B,OAAOF,WAAW,CAACK,IAAI,CAACH,KAAK,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}