{"ast": null, "code": "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) {\n  return React.createElement(RemoveScroll, __assign({}, props, {\n    ref: ref,\n    sideCar: SideCar\n  }));\n});\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;", "map": {"version": 3, "names": ["__assign", "React", "RemoveScroll", "SideCar", "ReactRemoveScroll", "forwardRef", "props", "ref", "createElement", "sideCar", "classNames"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/Combination.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,MAAM;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAQN,KAAK,CAACO,aAAa,CAACN,YAAY,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IAAEC,GAAG,EAAEA,GAAG;IAAEE,OAAO,EAAEN;EAAQ,CAAC,CAAC,CAAC;AAAG,CAAC,CAAC;AACpKC,iBAAiB,CAACM,UAAU,GAAGR,YAAY,CAACQ,UAAU;AACtD,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}