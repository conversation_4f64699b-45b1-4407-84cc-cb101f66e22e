{"ast": null, "code": "import { useLayoutEffect as $dxlwH$useLayoutEffect } from \"react\";\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst $9f79659886946c16$export$e5c5a5f917a5871c = Boolean(globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) ? $dxlwH$useLayoutEffect : () => {};\nexport { $9f79659886946c16$export$e5c5a5f917a5871c as useLayoutEffect };", "map": {"version": 3, "names": ["$9f79659886946c16$export$e5c5a5f917a5871c", "Boolean", "globalThis", "document", "$dxlwH$useLayoutEffect"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-layout-effect\\dist\\packages\\react\\use-layout-effect\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-layout-effect\\dist\\packages\\react\\use-layout-effect\\src\\useLayoutEffect.tsx"], "sourcesContent": ["export { useLayoutEffect } from './useLayoutEffect';\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "mappings": ";;ACEA;;;;;;;AAOA,MAAMA,yCAAe,GAAGC,OAAO,CAACC,UAAD,aAACA,UAAD,uBAACA,UAAU,CAAEC,QAAb,CAAP,GAAgCC,sBAAhC,GAAwD,MAAM,EAAtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}