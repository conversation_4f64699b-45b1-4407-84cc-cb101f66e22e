{"ast": null, "code": "var alwaysContainsScroll = function (node) {\n  // textarea will always _contain_ scroll inside self. It only can be hidden\n  return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n  var styles = window.getComputedStyle(node);\n  return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n    // contains scroll inside self\n    !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible')\n  );\n};\nvar elementCouldBeVScrolled = function (node) {\n  return elementCanBeScrolled(node, 'overflowY');\n};\nvar elementCouldBeHScrolled = function (node) {\n  return elementCanBeScrolled(node, 'overflowX');\n};\nexport var locationCouldBeScrolled = function (axis, node) {\n  var current = node;\n  do {\n    // Skip over shadow root\n    if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n      current = current.host;\n    }\n    var isScrollable = elementCouldBeScrolled(axis, current);\n    if (isScrollable) {\n      var _a = getScrollVariables(axis, current),\n        s = _a[1],\n        d = _a[2];\n      if (s > d) {\n        return true;\n      }\n    }\n    current = current.parentNode;\n  } while (current && current !== document.body);\n  return false;\n};\nvar getVScrollVariables = function (_a) {\n  var scrollTop = _a.scrollTop,\n    scrollHeight = _a.scrollHeight,\n    clientHeight = _a.clientHeight;\n  return [scrollTop, scrollHeight, clientHeight];\n};\nvar getHScrollVariables = function (_a) {\n  var scrollLeft = _a.scrollLeft,\n    scrollWidth = _a.scrollWidth,\n    clientWidth = _a.clientWidth;\n  return [scrollLeft, scrollWidth, clientWidth];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n  return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n  return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n  /**\n   * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n   * and then increasingly negative as you scroll towards the end of the content.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n   */\n  return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n  var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n  var delta = directionFactor * sourceDelta;\n  // find scrollable target\n  var target = event.target;\n  var targetInLock = endTarget.contains(target);\n  var shouldCancelScroll = false;\n  var isDeltaPositive = delta > 0;\n  var availableScroll = 0;\n  var availableScrollTop = 0;\n  do {\n    var _a = getScrollVariables(axis, target),\n      position = _a[0],\n      scroll_1 = _a[1],\n      capacity = _a[2];\n    var elementScroll = scroll_1 - capacity - directionFactor * position;\n    if (position || elementScroll) {\n      if (elementCouldBeScrolled(axis, target)) {\n        availableScroll += elementScroll;\n        availableScrollTop += position;\n      }\n    }\n    target = target.parentNode;\n  } while (\n  // portaled content\n  !targetInLock && target !== document.body ||\n  // self content\n  targetInLock && (endTarget.contains(target) || endTarget === target));\n  if (isDeltaPositive && (noOverscroll && availableScroll === 0 || !noOverscroll && delta > availableScroll)) {\n    shouldCancelScroll = true;\n  } else if (!isDeltaPositive && (noOverscroll && availableScrollTop === 0 || !noOverscroll && -delta > availableScrollTop)) {\n    shouldCancelScroll = true;\n  }\n  return shouldCancelScroll;\n};", "map": {"version": 3, "names": ["alwaysContainsScroll", "node", "tagName", "elementCanBeScrolled", "overflow", "styles", "window", "getComputedStyle", "overflowY", "overflowX", "elementCouldBeVScrolled", "elementCouldBeHScrolled", "locationCouldBeScrolled", "axis", "current", "ShadowRoot", "host", "isScrollable", "elementCouldBeScrolled", "_a", "getScrollVariables", "s", "d", "parentNode", "document", "body", "getVScrollVariables", "scrollTop", "scrollHeight", "clientHeight", "getHScrollVariables", "scrollLeft", "scrollWidth", "clientWidth", "getDirectionFactor", "direction", "handleScroll", "end<PERSON>ar<PERSON>", "event", "sourceDelta", "noOverscroll", "directionFactor", "delta", "target", "targetInLock", "contains", "shouldCancelScroll", "isDeltaPositive", "availableScroll", "availableScrollTop", "position", "scroll_1", "capacity", "elementScroll"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/handleScroll.js"], "sourcesContent": ["var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];\n            if (s > d) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== document.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        target = target.parentNode;\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    if (isDeltaPositive && ((noOverscroll && availableScroll === 0) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && availableScrollTop === 0) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACvC;EACA,OAAOA,IAAI,CAACC,OAAO,KAAK,UAAU;AACtC,CAAC;AACD,IAAIC,oBAAoB,GAAG,SAAAA,CAAUF,IAAI,EAAEG,QAAQ,EAAE;EACjD,IAAIC,MAAM,GAAGC,MAAM,CAACC,gBAAgB,CAACN,IAAI,CAAC;EAC1C;IACA;IACAI,MAAM,CAACD,QAAQ,CAAC,KAAK,QAAQ;IACzB;IACA,EAAEC,MAAM,CAACG,SAAS,KAAKH,MAAM,CAACI,SAAS,IAAI,CAACT,oBAAoB,CAACC,IAAI,CAAC,IAAII,MAAM,CAACD,QAAQ,CAAC,KAAK,SAAS;EAAC;AACjH,CAAC;AACD,IAAIM,uBAAuB,GAAG,SAAAA,CAAUT,IAAI,EAAE;EAAE,OAAOE,oBAAoB,CAACF,IAAI,EAAE,WAAW,CAAC;AAAE,CAAC;AACjG,IAAIU,uBAAuB,GAAG,SAAAA,CAAUV,IAAI,EAAE;EAAE,OAAOE,oBAAoB,CAACF,IAAI,EAAE,WAAW,CAAC;AAAE,CAAC;AACjG,OAAO,IAAIW,uBAAuB,GAAG,SAAAA,CAAUC,IAAI,EAAEZ,IAAI,EAAE;EACvD,IAAIa,OAAO,GAAGb,IAAI;EAClB,GAAG;IACC;IACA,IAAI,OAAOc,UAAU,KAAK,WAAW,IAAID,OAAO,YAAYC,UAAU,EAAE;MACpED,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,IAAIC,YAAY,GAAGC,sBAAsB,CAACL,IAAI,EAAEC,OAAO,CAAC;IACxD,IAAIG,YAAY,EAAE;MACd,IAAIE,EAAE,GAAGC,kBAAkB,CAACP,IAAI,EAAEC,OAAO,CAAC;QAAEO,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC;QAAEG,CAAC,GAAGH,EAAE,CAAC,CAAC,CAAC;MAChE,IAAIE,CAAC,GAAGC,CAAC,EAAE;QACP,OAAO,IAAI;MACf;IACJ;IACAR,OAAO,GAAGA,OAAO,CAACS,UAAU;EAChC,CAAC,QAAQT,OAAO,IAAIA,OAAO,KAAKU,QAAQ,CAACC,IAAI;EAC7C,OAAO,KAAK;AAChB,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAAAA,CAAUP,EAAE,EAAE;EACpC,IAAIQ,SAAS,GAAGR,EAAE,CAACQ,SAAS;IAAEC,YAAY,GAAGT,EAAE,CAACS,YAAY;IAAEC,YAAY,GAAGV,EAAE,CAACU,YAAY;EAC5F,OAAO,CACHF,SAAS,EACTC,YAAY,EACZC,YAAY,CACf;AACL,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAAAA,CAAUX,EAAE,EAAE;EACpC,IAAIY,UAAU,GAAGZ,EAAE,CAACY,UAAU;IAAEC,WAAW,GAAGb,EAAE,CAACa,WAAW;IAAEC,WAAW,GAAGd,EAAE,CAACc,WAAW;EAC1F,OAAO,CACHF,UAAU,EACVC,WAAW,EACXC,WAAW,CACd;AACL,CAAC;AACD,IAAIf,sBAAsB,GAAG,SAAAA,CAAUL,IAAI,EAAEZ,IAAI,EAAE;EAC/C,OAAOY,IAAI,KAAK,GAAG,GAAGH,uBAAuB,CAACT,IAAI,CAAC,GAAGU,uBAAuB,CAACV,IAAI,CAAC;AACvF,CAAC;AACD,IAAImB,kBAAkB,GAAG,SAAAA,CAAUP,IAAI,EAAEZ,IAAI,EAAE;EAC3C,OAAOY,IAAI,KAAK,GAAG,GAAGa,mBAAmB,CAACzB,IAAI,CAAC,GAAG6B,mBAAmB,CAAC7B,IAAI,CAAC;AAC/E,CAAC;AACD,IAAIiC,kBAAkB,GAAG,SAAAA,CAAUrB,IAAI,EAAEsB,SAAS,EAAE;EAChD;AACJ;AACA;AACA;AACA;EACI,OAAOtB,IAAI,KAAK,GAAG,IAAIsB,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;AACvD,CAAC;AACD,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUvB,IAAI,EAAEwB,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAE;EACnF,IAAIC,eAAe,GAAGP,kBAAkB,CAACrB,IAAI,EAAEP,MAAM,CAACC,gBAAgB,CAAC8B,SAAS,CAAC,CAACF,SAAS,CAAC;EAC5F,IAAIO,KAAK,GAAGD,eAAe,GAAGF,WAAW;EACzC;EACA,IAAII,MAAM,GAAGL,KAAK,CAACK,MAAM;EACzB,IAAIC,YAAY,GAAGP,SAAS,CAACQ,QAAQ,CAACF,MAAM,CAAC;EAC7C,IAAIG,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,eAAe,GAAGL,KAAK,GAAG,CAAC;EAC/B,IAAIM,eAAe,GAAG,CAAC;EACvB,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,GAAG;IACC,IAAI9B,EAAE,GAAGC,kBAAkB,CAACP,IAAI,EAAE8B,MAAM,CAAC;MAAEO,QAAQ,GAAG/B,EAAE,CAAC,CAAC,CAAC;MAAEgC,QAAQ,GAAGhC,EAAE,CAAC,CAAC,CAAC;MAAEiC,QAAQ,GAAGjC,EAAE,CAAC,CAAC,CAAC;IAC/F,IAAIkC,aAAa,GAAGF,QAAQ,GAAGC,QAAQ,GAAGX,eAAe,GAAGS,QAAQ;IACpE,IAAIA,QAAQ,IAAIG,aAAa,EAAE;MAC3B,IAAInC,sBAAsB,CAACL,IAAI,EAAE8B,MAAM,CAAC,EAAE;QACtCK,eAAe,IAAIK,aAAa;QAChCJ,kBAAkB,IAAIC,QAAQ;MAClC;IACJ;IACAP,MAAM,GAAGA,MAAM,CAACpB,UAAU;EAC9B,CAAC;EACD;EACC,CAACqB,YAAY,IAAID,MAAM,KAAKnB,QAAQ,CAACC,IAAI;EACtC;EACCmB,YAAY,KAAKP,SAAS,CAACQ,QAAQ,CAACF,MAAM,CAAC,IAAIN,SAAS,KAAKM,MAAM,CAAE;EAC1E,IAAII,eAAe,KAAMP,YAAY,IAAIQ,eAAe,KAAK,CAAC,IAAM,CAACR,YAAY,IAAIE,KAAK,GAAGM,eAAgB,CAAC,EAAE;IAC5GF,kBAAkB,GAAG,IAAI;EAC7B,CAAC,MACI,IAAI,CAACC,eAAe,KACnBP,YAAY,IAAIS,kBAAkB,KAAK,CAAC,IAAM,CAACT,YAAY,IAAI,CAACE,KAAK,GAAGO,kBAAmB,CAAC,EAAE;IAChGH,kBAAkB,GAAG,IAAI;EAC7B;EACA,OAAOA,kBAAkB;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}