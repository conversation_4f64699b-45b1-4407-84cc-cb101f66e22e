{"ast": null, "code": "import { useRef as $lwiWj$useRef, useEffect as $lwiWj$useEffect, useMemo as $lwiWj$useMemo } from \"react\";\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction $b1b2314f5f9a1d84$export$25bec8c6f54ee79a(callback) {\n  const callbackRef = $lwiWj$useRef(callback);\n  $lwiWj$useEffect(() => {\n    callbackRef.current = callback;\n  }); // https://github.com/facebook/react/issues/19240\n  return $lwiWj$useMemo(() => (...args) => {\n    var _callbackRef$current;\n    return (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 ? void 0 : _callbackRef$current.call(callbackRef, ...args);\n  }, []);\n}\nexport { $b1b2314f5f9a1d84$export$25bec8c6f54ee79a as useCallbackRef };", "map": {"version": 3, "names": ["$b1b2314f5f9a1d84$export$25bec8c6f54ee79a", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "$lwiWj$useRef", "$lwiWj$useEffect", "current", "$lwiWj$useMemo", "args", "_callbackRef$current", "call"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-callback-ref\\dist\\packages\\react\\use-callback-ref\\src\\index.ts", "D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\@radix-ui\\react-select\\node_modules\\@radix-ui\\react-use-callback-ref\\dist\\packages\\react\\use-callback-ref\\src\\useCallbackRef.tsx"], "sourcesContent": ["export { useCallbackRef } from './useCallbackRef';\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "mappings": ";;ACEA;;;;AAIA,SAASA,yCAATC,CAA2DC,QAA3D,EAAuF;EACrF,MAAMC,WAAW,GAAGC,aAAA,CAAaF,QAAb,CAApB;EAEAG,gBAAA,CAAgB,MAAM;IACpBF,WAAW,CAACG,OAAZ,GAAsBJ,QAAtB;GADF,EAHqF,CAOrF;EACA,OAAOK,cAAA,CAAc,MAAO,CAAI,GAAAC,IAAJ,KAA5B;IAA4B,IAAAC,oBAAA;IAAA,QAAAA,oBAAA,GAAaN,WAAW,CAACG,OAAzB,cAAAG,oBAAA,uBAAaA,oBAAA,CAAAC,IAAA,CAAAP,WAAW,KAAcK,IAAd,CAAxB;GAArB,EAAwE,EAAxE,CAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}