{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ReplaceAll = createLucideIcon(\"ReplaceAll\", [[\"path\", {\n  d: \"M14 4c0-1.1.9-2 2-2\",\n  key: \"1mvvbw\"\n}], [\"path\", {\n  d: \"M20 2c1.1 0 2 .9 2 2\",\n  key: \"1mj6oe\"\n}], [\"path\", {\n  d: \"M22 8c0 1.1-.9 2-2 2\",\n  key: \"v1wql3\"\n}], [\"path\", {\n  d: \"M16 10c-1.1 0-2-.9-2-2\",\n  key: \"821ux0\"\n}], [\"path\", {\n  d: \"m3 7 3 3 3-3\",\n  key: \"x25e72\"\n}], [\"path\", {\n  d: \"M6 10V5c0-1.7 1.3-3 3-3h1\",\n  key: \"13af7h\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"17ihk4\"\n}], [\"path\", {\n  d: \"M14 14c1.1 0 2 .9 2 2v4c0 1.1-.9 2-2 2\",\n  key: \"1w9p8c\"\n}], [\"path\", {\n  d: \"M20 14c1.1 0 2 .9 2 2v4c0 1.1-.9 2-2 2\",\n  key: \"m45eaa\"\n}]]);\nexport { ReplaceAll as default };", "map": {"version": 3, "names": ["ReplaceAll", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["D:\\MyProjects\\Git Hub Projects\\Grocery Tracker\\frontend\\node_modules\\lucide-react\\src\\icons\\replace-all.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ReplaceAll\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgNGMwLTEuMS45LTIgMi0yIiAvPgogIDxwYXRoIGQ9Ik0yMCAyYzEuMSAwIDIgLjkgMiAyIiAvPgogIDxwYXRoIGQ9Ik0yMiA4YzAgMS4xLS45IDItMiAyIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMGMtMS4xIDAtMi0uOS0yLTIiIC8+CiAgPHBhdGggZD0ibTMgNyAzIDMgMy0zIiAvPgogIDxwYXRoIGQ9Ik02IDEwVjVjMC0xLjcgMS4zLTMgMy0zaDEiIC8+CiAgPHJlY3Qgd2lkdGg9IjgiIGhlaWdodD0iOCIgeD0iMiIgeT0iMTQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNGMxLjEgMCAyIC45IDIgMnY0YzAgMS4xLS45IDItMiAyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxNGMxLjEgMCAyIC45IDIgMnY0YzAgMS4xLS45IDItMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/replace-all\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ReplaceAll = createLucideIcon('ReplaceAll', [\n  ['path', { d: 'M14 4c0-1.1.9-2 2-2', key: '1mvvbw' }],\n  ['path', { d: 'M20 2c1.1 0 2 .9 2 2', key: '1mj6oe' }],\n  ['path', { d: 'M22 8c0 1.1-.9 2-2 2', key: 'v1wql3' }],\n  ['path', { d: 'M16 10c-1.1 0-2-.9-2-2', key: '821ux0' }],\n  ['path', { d: 'm3 7 3 3 3-3', key: 'x25e72' }],\n  ['path', { d: 'M6 10V5c0-1.7 1.3-3 3-3h1', key: '13af7h' }],\n  [\n    'rect',\n    { width: '8', height: '8', x: '2', y: '14', rx: '2', key: '17ihk4' },\n  ],\n  ['path', { d: 'M14 14c1.1 0 2 .9 2 2v4c0 1.1-.9 2-2 2', key: '1w9p8c' }],\n  ['path', { d: 'M20 14c1.1 0 2 .9 2 2v4c0 1.1-.9 2-2 2', key: 'm45eaa' }],\n]);\n\nexport default ReplaceAll;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}