{"ast": null, "code": "import RemoveScroll from './Combination';\nexport { RemoveScroll };", "map": {"version": 3, "names": ["RemoveScroll"], "sources": ["D:/MyProjects/Git Hub Projects/Grocery Tracker/frontend/node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/index.js"], "sourcesContent": ["import RemoveScroll from './Combination';\nexport { RemoveScroll };\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,eAAe;AACxC,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}