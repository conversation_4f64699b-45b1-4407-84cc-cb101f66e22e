{"name": "grocery-tracker", "version": "1.0.0", "description": "A modern, full-stack web application for tracking your grocery inventory and managing pantry items", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && python -m uvicorn server:app --reload --host 0.0.0.0 --port 8000", "dev:frontend": "cd frontend && npm start", "install:all": "npm install && cd frontend && npm install", "build": "cd frontend && npm run build", "test": "cd frontend && npm test"}, "keywords": ["grocery", "tracker", "inventory", "pantry", "<PERSON><PERSON><PERSON>", "react", "mongodb"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/grocery-tracker.git"}, "bugs": {"url": "https://github.com/yourusername/grocery-tracker/issues"}, "homepage": "https://github.com/yourusername/grocery-tracker#readme"}