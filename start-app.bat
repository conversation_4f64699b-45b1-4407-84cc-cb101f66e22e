@echo off
echo ========================================
echo    Grocery Tracker - Local Development
echo ========================================
echo.

echo This script will start both the backend and frontend servers.
echo.
echo Backend will run on: http://localhost:8000
echo Frontend will run on: http://localhost:3000
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul
echo.

REM Start backend in a new window
echo Starting backend server...
start "Grocery Tracker Backend" cmd /c "start-backend.bat"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in a new window
echo Starting frontend server...
start "Grocery Tracker Frontend" cmd /c "start-frontend.bat"

echo.
echo Both servers are starting in separate windows.
echo.
echo Backend API: http://localhost:8000
echo Frontend App: http://localhost:3000
echo.
echo Close this window or press any key to exit...
pause >nul
