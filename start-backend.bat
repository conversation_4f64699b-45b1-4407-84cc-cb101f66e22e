@echo off
echo Starting Grocery Tracker Backend...
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo Creating Python virtual environment...
    python -m venv venv
    echo.
)

REM Activate virtual environment and install dependencies
echo Activating virtual environment and installing dependencies...
venv\Scripts\python.exe -m pip install -r backend\requirements.txt
echo.

REM Start the backend server
echo Starting FastAPI server on http://localhost:8000
echo Press Ctrl+C to stop the server
echo.
venv\Scripts\python.exe -m uvicorn backend.server:app --reload --host 0.0.0.0 --port 8000
