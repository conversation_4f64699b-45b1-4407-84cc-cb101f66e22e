Write-Host "Starting Grocery Tracker Backend..." -ForegroundColor Green
Write-Host ""

# Check if virtual environment exists
if (-not (Test-Path "venv\Scripts\python.exe")) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host ""
}

# Install dependencies
Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
& "venv\Scripts\python.exe" -m pip install -r backend\requirements.txt
Write-Host ""

# Start the backend server
Write-Host "Starting FastAPI server on http://localhost:8000" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

& "venv\Scripts\python.exe" -m uvicorn backend.server:app --reload --host 0.0.0.0 --port 8000
